# Step 45: Bill Status Update Job - Complete Implementation Documentation

## Overview

Step 45 implements the automated bill status update job that forms the foundation of our feedback loop system. This Lambda function runs daily to check for legislative status changes from the OpenStates API and updates our database accordingly.

## Architecture

### System Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   EventBridge   │───▶│  Lambda Function │───▶│   PostgreSQL    │
│  (Daily Cron)   │    │ Bill Status Job  │    │    Database     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │  OpenStates API  │
                       │   (External)     │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   SQS Queue      │
                       │ (For Step 46)    │
                       └──────────────────┘
```

### Data Flow

1. **EventBridge Trigger**: Runs Lambda daily at scheduled time
2. **Database Query**: Lambda queries for all active bills
3. **API Calls**: For each bill, fetches latest status from OpenStates
4. **Status Comparison**: Compares API status with database status
5. **Change Detection**: Identifies significant status changes
6. **Database Update**: Creates audit records and updates bill status
7. **Queue Message**: Sends notification messages to SQS for Step 46

## Database Schema

### New Table: `bill_status_pipeline`

```sql
CREATE TABLE bill_status_pipeline (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    bill_id UUID NOT NULL REFERENCES bills(id),
    previous_status VARCHAR,
    current_status VARCHAR NOT NULL,
    status_changed_at TIMESTAMP NOT NULL,
    detected_at TIMESTAMP NOT NULL,
    external_data JSONB,
    vote_details JSONB,
    notification_sent BOOLEAN NOT NULL DEFAULT FALSE,
    is_significant_change BOOLEAN NOT NULL DEFAULT FALSE,
    notes TEXT
);

-- Indexes for efficient querying
CREATE INDEX idx_bill_status_pipeline_bill_id ON bill_status_pipeline(bill_id);
CREATE INDEX idx_bill_status_pipeline_current_status ON bill_status_pipeline(current_status);
CREATE INDEX idx_bill_status_pipeline_detected_at ON bill_status_pipeline(detected_at);
CREATE INDEX idx_bill_status_pipeline_notification_sent ON bill_status_pipeline(notification_sent);
CREATE INDEX idx_bill_status_pipeline_is_significant_change ON bill_status_pipeline(is_significant_change);
```

### Field Descriptions

- **bill_id**: Foreign key to the bills table
- **previous_status**: The bill's status before the change
- **current_status**: The bill's new status after the change
- **status_changed_at**: When the status actually changed (from API)
- **detected_at**: When our system detected the change
- **external_data**: Raw JSON data from OpenStates API
- **vote_details**: Extracted vote information if available
- **notification_sent**: Whether users have been notified of this change
- **is_significant_change**: Whether this change warrants user notification
- **notes**: Additional context about the status change

## Implementation Files

### Core Service Layer
- **`apps/api/app/services/bill_status_update.py`**: Main service class with business logic
- **`apps/api/app/models/bill.py`**: Updated with BillStatusPipeline model

### Lambda Function
- **`apps/lambda/bill_status_update/handler.py`**: Lambda entry point
- **`apps/lambda/bill_status_update/shared/database.py`**: Database connection utilities
- **`apps/lambda/bill_status_update/shared/bill_status_service.py`**: Simplified service for Lambda

### Infrastructure
- **`infrastructure/modernaction/modernaction_stack.py`**: CDK stack with Lambda definition
- **`apps/api/alembic/versions/003_add_bill_status_pipeline.py`**: Database migration

### Testing
- **`apps/api/tests/test_bill_status_update.py`**: Service unit tests
- **`apps/lambda/bill_status_update/test_lambda.py`**: Lambda integration tests

### Configuration
- **`apps/lambda/bill_status_update/.env.example`**: Environment variables template
- **`apps/lambda/bill_status_update/requirements.txt`**: Lambda dependencies
- **`apps/lambda/bill_status_update/deploy.sh`**: Manual deployment script

## Environment Variables

### Required for Lambda Function

```bash
# OpenStates API Integration
OPEN_STATES_API_KEY=your_openstates_api_key_here

# Database Connection (Option 1: Full URL)
DATABASE_URL=postgresql://user:pass@host:port/dbname

# Database Connection (Option 2: Individual Components)
DB_HOST=your_database_host
DB_PORT=5432
DB_NAME=modernaction
DB_USER=your_database_user
DB_PASSWORD=your_database_password

# AWS Configuration
AWS_REGION=us-east-1
SQS_QUEUE_URL=https://sqs.region.amazonaws.com/account/queue-name
```

### CDK Deployment Variables

The CDK stack automatically configures these from AWS Secrets Manager:
- Database credentials from RDS secret
- OpenStates API key from `modernaction/openstates/api_key` secret
- SQS queue URL from created queue

## Status Mapping Logic

### Active Statuses (Checked for Updates)
- `draft` - Bill is being drafted
- `introduced` - Bill has been introduced
- `committee` - Bill is in committee review
- `floor` - Bill is on the floor for voting

### Final Statuses (No Longer Checked)
- `passed` - Bill passed both chambers
- `signed` - Bill signed into law
- `vetoed` - Bill was vetoed
- `failed` - Bill failed to pass

### Significant Change Detection

Changes are considered significant if:
1. **Status Progression**: Any forward movement through the legislative process
2. **Final Outcomes**: Any transition to passed, signed, vetoed, or failed
3. **Vote Events**: Any action containing vote-related keywords

## Deployment Guide

### Prerequisites

1. **AWS Account**: With appropriate permissions
2. **OpenStates API Key**: Register at openstates.org
3. **CDK Bootstrap**: Run `cdk bootstrap` in your AWS account
4. **Database**: PostgreSQL instance (RDS recommended)

### Step 1: Configure Secrets

```bash
# Create OpenStates API key secret
aws secretsmanager create-secret \
  --name "modernaction/openstates/api_key" \
  --description "OpenStates API key for bill status updates" \
  --secret-string "your_actual_api_key_here"
```

### Step 2: Deploy Infrastructure

```bash
cd infrastructure
npm install
cdk deploy
```

### Step 3: Apply Database Migration

```bash
cd apps/api
poetry run alembic upgrade head
```

### Step 4: Verify Deployment

```bash
# Test Lambda function
aws lambda invoke \
  --function-name modernaction-bill-status-update-dev \
  --payload '{}' \
  response.json

# Check logs
aws logs describe-log-groups \
  --log-group-name-prefix "/aws/lambda/modernaction-bill-status-update"
```

## Monitoring and Observability

### CloudWatch Metrics

Monitor these key metrics:
- **Duration**: Lambda execution time
- **Errors**: Function failures
- **Invocations**: Successful executions
- **Throttles**: Rate limiting issues

### CloudWatch Logs

Key log messages to monitor:
- `"Starting bill status update job"` - Job initiation
- `"Found X active bills to check"` - Bills being processed
- `"Updated bill X: status -> status"` - Successful updates
- `"Bill status update complete: X/Y bills updated"` - Job completion

### Alerts to Configure

1. **Lambda Failures**: Alert on any function errors
2. **Long Execution**: Alert if duration > 10 minutes
3. **No Updates**: Alert if no bills updated for 7+ days
4. **API Failures**: Alert on OpenStates API errors

## Testing

### Running Tests Locally

```bash
# API Service Tests
cd apps/api
python -m pytest tests/test_bill_status_update.py -v

# Lambda Integration Tests
cd apps/lambda/bill_status_update
python -m pytest test_lambda.py -v

# Local Lambda Testing
python handler.py
```

### Test Coverage

- **Unit Tests**: 9 tests covering service methods
- **Integration Tests**: 9 tests covering Lambda handler
- **Error Scenarios**: Database failures, API timeouts, malformed data
- **Edge Cases**: No bills to update, partial failures

## Security Considerations

### Access Control
- Lambda runs with minimal IAM permissions
- Database access limited to specific tables
- Secrets Manager integration for sensitive data

### Network Security
- Lambda runs in private VPC subnets
- Security groups restrict database access
- No direct internet access (uses NAT Gateway)

### Data Protection
- All API responses stored as encrypted JSONB
- Database connections use SSL/TLS
- Audit trail preserves data integrity

## Performance Optimization

### Lambda Configuration
- **Memory**: 512 MB (optimal for database operations)
- **Timeout**: 15 minutes (handles large bill volumes)
- **Concurrency**: 1 (prevents database connection issues)

### Database Optimization
- Indexes on frequently queried columns
- Connection pooling disabled (Lambda best practice)
- Query optimization for active bill selection

### API Rate Limiting
- Sequential processing to respect OpenStates limits
- Exponential backoff on API failures
- Graceful handling of rate limit responses

## Troubleshooting

### Common Issues

1. **Database Connection Timeout**
   - Check VPC configuration
   - Verify security group rules
   - Confirm RDS instance status

2. **OpenStates API Failures**
   - Verify API key in Secrets Manager
   - Check rate limiting
   - Review API response format changes

3. **Lambda Timeout**
   - Increase timeout setting
   - Optimize database queries
   - Consider batch processing

### Debug Commands

```bash
# Check Lambda logs
aws logs tail /aws/lambda/modernaction-bill-status-update-dev --follow

# Test database connection
aws rds describe-db-instances --db-instance-identifier your-db-instance

# Verify secrets
aws secretsmanager get-secret-value --secret-id modernaction/openstates/api_key
```

## Integration with Step 46

This implementation prepares for Step 46 (Vote Update Notification System):

1. **SQS Queue**: Created and configured for notification messages
2. **Significant Changes**: Flagged in database for notification processing
3. **Message Format**: Designed for easy consumption by notification Lambda
4. **Audit Trail**: Complete history available for notification context

### Message Format for Step 46

```json
{
  "bill_id": "uuid-here",
  "new_status": "passed",
  "previous_status": "floor",
  "status_changed_at": "2024-07-18T12:00:00Z",
  "is_significant_change": true,
  "vote_details": {
    "action_description": "Passed final vote",
    "action_date": "2024-07-18T10:30:00Z",
    "organization": "House",
    "result": "pass"
  }
}
```

## Maintenance

### Regular Tasks
- Monitor CloudWatch logs weekly
- Review API usage monthly
- Update dependencies quarterly
- Test disaster recovery annually

### Scaling Considerations
- Current design handles ~1000 bills efficiently
- For larger volumes, consider batch processing
- Database connection pooling may be needed at scale
- Consider Lambda provisioned concurrency for consistent performance

---

This completes the comprehensive documentation for Step 45. The system is production-ready and fully documented for deployment, monitoring, and maintenance.
