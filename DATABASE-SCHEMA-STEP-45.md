# Database Schema Documentation - Step 45: Bill Status Pipeline

## Overview

Step 45 introduces the `bill_status_pipeline` table to provide a comprehensive audit trail of all bill status changes. This table enables tracking of legislative progress, user notifications, and historical analysis.

## New Table: `bill_status_pipeline`

### Table Definition

```sql
CREATE TABLE bill_status_pipeline (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    bill_id UUID NOT NULL,
    previous_status VARCHAR,
    current_status VARCHAR NOT NULL,
    status_changed_at TIMESTAMP NOT NULL,
    detected_at TIMESTAMP NOT NULL,
    external_data JSONB,
    vote_details JSONB,
    notification_sent BOOLEAN NOT NULL DEFAULT FALSE,
    is_significant_change BOOLEAN NOT NULL DEFAULT FALSE,
    notes TEXT,
    
    CONSTRAINT fk_bill_status_pipeline_bill_id 
        FOREIGN KEY (bill_id) REFERENCES bills(id) ON DELETE CASCADE
);
```

### Indexes

```sql
-- Primary key index (automatic)
CREATE UNIQUE INDEX bill_status_pipeline_pkey ON bill_status_pipeline(id);

-- Foreign key index for efficient joins
CREATE INDEX idx_bill_status_pipeline_bill_id ON bill_status_pipeline(bill_id);

-- Query optimization indexes
CREATE INDEX idx_bill_status_pipeline_current_status ON bill_status_pipeline(current_status);
CREATE INDEX idx_bill_status_pipeline_detected_at ON bill_status_pipeline(detected_at);
CREATE INDEX idx_bill_status_pipeline_notification_sent ON bill_status_pipeline(notification_sent);
CREATE INDEX idx_bill_status_pipeline_is_significant_change ON bill_status_pipeline(is_significant_change);

-- Composite index for notification queries
CREATE INDEX idx_bill_status_pipeline_notification_pending 
    ON bill_status_pipeline(is_significant_change, notification_sent) 
    WHERE is_significant_change = TRUE AND notification_sent = FALSE;
```

## Field Specifications

### Core Fields

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, NOT NULL | Unique identifier for each status change record |
| `created_at` | TIMESTAMP | NOT NULL, DEFAULT NOW() | When the record was created in our system |
| `updated_at` | TIMESTAMP | NOT NULL, DEFAULT NOW() | When the record was last modified |

### Relationship Fields

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `bill_id` | UUID | NOT NULL, FOREIGN KEY | References `bills.id` - the bill that changed status |

### Status Tracking Fields

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `previous_status` | VARCHAR | NULL allowed | The bill's status before this change (NULL for first record) |
| `current_status` | VARCHAR | NOT NULL | The bill's status after this change |
| `status_changed_at` | TIMESTAMP | NOT NULL | When the status actually changed (from external API) |
| `detected_at` | TIMESTAMP | NOT NULL | When our system detected and recorded the change |

### Data Storage Fields

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `external_data` | JSONB | NULL allowed | Complete raw response from OpenStates API |
| `vote_details` | JSONB | NULL allowed | Extracted vote information if available |
| `notes` | TEXT | NULL allowed | Additional context or comments about the change |

### Processing Fields

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `notification_sent` | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether users have been notified of this change |
| `is_significant_change` | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether this change warrants user notification |

## Status Values

### Valid Status Values

The `previous_status` and `current_status` fields use these standardized values:

| Status | Description | Category |
|--------|-------------|----------|
| `draft` | Bill is being drafted | Active |
| `introduced` | Bill has been introduced | Active |
| `committee` | Bill is in committee review | Active |
| `floor` | Bill is on the floor for voting | Active |
| `passed` | Bill passed both chambers | Final |
| `signed` | Bill signed into law | Final |
| `vetoed` | Bill was vetoed | Final |
| `failed` | Bill failed to pass | Final |

### Status Progression

Normal progression through the legislative process:
```
draft → introduced → committee → floor → passed → signed
                                    ↓
                                  failed
                                    ↓
                                  vetoed (from signed)
```

## JSON Field Schemas

### `external_data` Field

Contains the complete OpenStates API response:

```json
{
  "id": "ocd-bill/12345",
  "title": "An Act to improve healthcare access",
  "identifier": "HB 123",
  "classification": ["bill"],
  "subject": ["Healthcare"],
  "abstracts": [...],
  "other_titles": [...],
  "other_identifiers": [...],
  "actions": [
    {
      "organization": {
        "id": "ocd-organization/...",
        "name": "House",
        "classification": "lower"
      },
      "description": "Introduced in House",
      "date": "2024-01-15",
      "classification": ["introduction"],
      "order": 1
    }
  ],
  "sponsorships": [...],
  "related_bills": [...],
  "versions": [...],
  "documents": [...],
  "sources": [...]
}
```

### `vote_details` Field

Extracted and simplified vote information:

```json
{
  "action_description": "Passed House floor vote",
  "action_date": "2024-01-20T14:30:00Z",
  "organization": "House",
  "result": "pass",
  "vote_counts": {
    "yes": 65,
    "no": 34,
    "abstain": 1
  },
  "vote_breakdown": [
    {
      "legislator": "John Doe",
      "vote": "yes",
      "party": "Democratic"
    }
  ]
}
```

## Relationships

### With `bills` Table

```sql
-- One-to-many relationship
-- One bill can have many status change records
SELECT b.bill_number, b.title, COUNT(bsp.id) as status_changes
FROM bills b
LEFT JOIN bill_status_pipeline bsp ON b.id = bsp.bill_id
GROUP BY b.id, b.bill_number, b.title;
```

### SQLAlchemy Relationship

```python
# In Bill model
class Bill(Base):
    # ... other fields ...
    status_history = relationship(
        "BillStatusPipeline", 
        back_populates="bill", 
        cascade="all, delete-orphan",
        order_by="BillStatusPipeline.detected_at.desc()"
    )

# In BillStatusPipeline model
class BillStatusPipeline(Base):
    # ... other fields ...
    bill = relationship("Bill", back_populates="status_history")
```

## Common Queries

### Find Bills Needing Notifications

```sql
SELECT bsp.*, b.bill_number, b.title
FROM bill_status_pipeline bsp
JOIN bills b ON bsp.bill_id = b.id
WHERE bsp.is_significant_change = TRUE 
  AND bsp.notification_sent = FALSE
ORDER BY bsp.detected_at DESC;
```

### Get Status History for a Bill

```sql
SELECT 
    bsp.detected_at,
    bsp.previous_status,
    bsp.current_status,
    bsp.is_significant_change,
    bsp.notification_sent
FROM bill_status_pipeline bsp
WHERE bsp.bill_id = $1
ORDER BY bsp.detected_at ASC;
```

### Find Recent Status Changes

```sql
SELECT 
    b.bill_number,
    b.title,
    bsp.previous_status,
    bsp.current_status,
    bsp.detected_at
FROM bill_status_pipeline bsp
JOIN bills b ON bsp.bill_id = b.id
WHERE bsp.detected_at >= NOW() - INTERVAL '24 hours'
ORDER BY bsp.detected_at DESC;
```

### Count Status Changes by Type

```sql
SELECT 
    current_status,
    COUNT(*) as change_count,
    COUNT(CASE WHEN is_significant_change THEN 1 END) as significant_count
FROM bill_status_pipeline
WHERE detected_at >= NOW() - INTERVAL '30 days'
GROUP BY current_status
ORDER BY change_count DESC;
```

## Migration Information

### Migration File
- **Location**: `apps/api/alembic/versions/003_add_bill_status_pipeline.py`
- **Revision ID**: `003`
- **Revises**: `002`

### Migration Commands

```bash
# Generate migration (already done)
poetry run alembic revision --autogenerate -m "Add BillStatusPipeline model and relationships"

# Apply migration
poetry run alembic upgrade head

# Rollback migration (if needed)
poetry run alembic downgrade 002
```

## Data Integrity

### Constraints

1. **Foreign Key Constraint**: Ensures `bill_id` references valid bill
2. **NOT NULL Constraints**: Prevents missing critical data
3. **Default Values**: Ensures consistent data for boolean fields

### Validation Rules

1. **Status Values**: Should be validated at application level
2. **Timestamp Logic**: `status_changed_at` should not be in the future
3. **Progression Logic**: Status changes should follow logical progression
4. **Notification Logic**: Only significant changes should have notifications

### Data Cleanup

```sql
-- Remove orphaned records (if foreign key constraint is disabled)
DELETE FROM bill_status_pipeline 
WHERE bill_id NOT IN (SELECT id FROM bills);

-- Clean up old records (optional, for data retention)
DELETE FROM bill_status_pipeline 
WHERE detected_at < NOW() - INTERVAL '2 years'
  AND notification_sent = TRUE;
```

## Performance Considerations

### Index Usage

- **bill_id index**: Optimizes joins with bills table
- **detected_at index**: Optimizes time-based queries
- **notification indexes**: Optimizes notification processing queries
- **composite index**: Optimizes pending notification queries

### Query Optimization

```sql
-- Use EXPLAIN to analyze query performance
EXPLAIN ANALYZE 
SELECT * FROM bill_status_pipeline 
WHERE is_significant_change = TRUE 
  AND notification_sent = FALSE;

-- Consider partitioning for large datasets
-- Partition by detected_at for time-based queries
```

### Storage Optimization

- **JSONB fields**: Use GIN indexes for complex JSON queries
- **Text fields**: Consider full-text search indexes if needed
- **Archival**: Move old records to archive tables

## Monitoring and Maintenance

### Health Checks

```sql
-- Check for data consistency
SELECT COUNT(*) as total_records FROM bill_status_pipeline;

-- Check for recent activity
SELECT COUNT(*) as recent_changes 
FROM bill_status_pipeline 
WHERE detected_at >= NOW() - INTERVAL '24 hours';

-- Check notification backlog
SELECT COUNT(*) as pending_notifications
FROM bill_status_pipeline 
WHERE is_significant_change = TRUE 
  AND notification_sent = FALSE;
```

### Maintenance Tasks

1. **Regular Cleanup**: Archive old records
2. **Index Maintenance**: Reindex periodically
3. **Statistics Update**: Update table statistics
4. **Backup Verification**: Ensure backups include new table

---

This database schema provides a robust foundation for tracking bill status changes and supporting the automated feedback system in Step 45 and beyond.
