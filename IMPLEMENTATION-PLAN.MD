

ModernAction.io: Master Implementation Plan (v3.0)

Version: 3.0 (Flawless)
Status: In Progress
Lead: Lead Technical Advisor
Timeline: 6 Months
Related Document: ModernAction.io - The Master Blueprint (Internal Link)

A Note to Our Developers: The ModernAction.io Way

This document is your guide. It is intentionally verbose and prescriptive to ensure clarity and minimize ambiguity. Your primary directive is to build with quality and document your work.

Documentation is Not Optional: After completing each step, you must update our project documentation. Explain the "why" behind your implementation choices. If you deviate from this plan for a good reason, document it.

Keep a Log: At the end of each step, there is a Developer Log section. You are encouraged to add brief, dated notes here about your progress, challenges, or decisions. This makes this a living document.

Testing is Paramount: After each logical feature is complete, you must write and run end-to-end tests. We are using a test-driven mindset. If a test fails, do not proceed.

What to do if a test fails:

STOP: Do not merge the Pull Request.

DOCUMENT: In the PR comments and our issue tracker, document the failure with screenshots, logs, and steps to reproduce.

ITERATE: On your feature branch, work through the bug. Document your debugging process and the final fix in your commit messages.

RE-TEST: Re-run all relevant unit, integration, and end-to-end tests until they pass.

MERGE: Once all tests are green, you may request a final review for merging.

Let's build the best damn civic action platform in the world.

✅ Phase 1: Foundation & Core Services (Months 1-2) - COMPLETE ✅

Goal: Establish the project's technical foundation, set up the development environment, and build the core data models and services.

Sprint 1: Project Setup & AWS Foundation (Weeks 1-2)

Status: ✅ COMPLETE

Sprint 2: Database Modeling & Backend Setup (Weeks 3-4)

Status: ✅ COMPLETE

Sprint 3: Seeding the Database & API Foundation (Weeks 5-6)

Status: ✅ COMPLETE

Developer Logs & Key Decisions:

Decision: A robust service layer architecture was implemented to separate business logic from API endpoints, enhancing testability and maintainability.

Decision: Comprehensive Pydantic schemas were created for all models, providing strong typing, validation, and automatic API documentation.

Challenge: The initial database implementation using SQLite for testing created conflicts with PostgreSQL-specific features like JSONB and UUID types.

Solution: A cross-database compatibility strategy was developed, and later (in Sprint 6 remediation) the entire test suite was migrated to run against a PostgreSQL test database, eliminating this technical debt permanently.

Decision: Incremental model development was adopted, allowing services to be built and tested independently by temporarily commenting out relationships to models that did not yet exist. This was crucial for maintaining development velocity.

(Full developer logs for Steps 14, 15, and 16 are archived)

✅ Phase 2: Core Action Loop & AI Integration (Months 3-4) - COMPLETE ✅

Goal: Build the end-to-end user flow for taking action and integrate our core AI features.

Sprint 4: Frontend-Backend Connection & Campaign Pages (Weeks 7-8)

Status: ✅ COMPLETE

Developer Logs & Key Decisions:

Decision: A centralized API service layer (apiClient.ts) was created using axios to provide a single, type-safe source of truth for all frontend-to-backend communication.

Decision: A modular component architecture was enforced, breaking down complex pages into smaller, single-purpose, and reusable components.

Decision: Server-Side Rendering (getServerSideProps) was used for initial page loads to ensure fast perceived performance and strong SEO, while SWR was used for client-side dynamic data (like live stats) to keep pages feeling alive.

(Full developer logs for Steps 21-25 are archived)

Sprint 5: AI Integration - Bill Summarization (Weeks 9-10)

Status: ✅ COMPLETE

Developer Logs & Key Decisions:

Decision: A singleton pattern using @lru_cache was implemented for loading ML models. This was a critical performance optimization to prevent reloading heavy models on every API call.

Decision: All AI summarization for new bills created via the API was moved to an asynchronous background task.

Rationale: Performance benchmarks showed that synchronous AI inference would add 1-2 seconds to the API response time. Making this process asynchronous keeps the user-facing API fast and responsive (<100ms).

Decision: Specialized operational endpoints (e.g., regenerate-summary, ai-summary-status) were proactively created to provide full administrative control over the AI system.

(Full developer logs for Steps 27-32 are archived)

Sprint 6: The Action Modal & Email Integration (Weeks 11-12)

Status: ✅ COMPLETE (Including Remediation)

Developer Logs & Key Decisions:

Decision: Headless UI components were used to build the ActionModal, ensuring a high standard of accessibility from the start.

Challenge: The initial test suite was found to be brittle and unreliable, with a mix of failing tests and a critical dependency on SQLite.

Solution: A mandatory remediation phase was executed. The developer successfully migrated the entire backend test suite to run against a containerized PostgreSQL database, established perfect transaction-based test isolation, and fixed all failing tests, resulting in a 100% green build (176/176 tests passing). This was a pivotal moment that eliminated significant technical debt.

(Full developer logs detailing the successful remediation are archived)

Sprint 7: AI Language Assist & Tweet Integration (Weeks 13-14)

Status: ✅ COMPLETE

Developer Logs & Key Decisions:

Decision: The background action processor was refactored to be multi-channel, capable of handling both emails and tweets from a single user request.

Decision: A PARTIAL success status was added to the Action model to correctly handle scenarios where one channel (e.g., email) succeeds but another (e.g., tweet) fails. This provides a more accurate record of the action's outcome.

Decision: The AI personalization feature was enhanced with "smart prompting," using campaign context to generate more relevant and persuasive messages.

(Full developer logs for Steps 40-44 are archived)

⏳ Phase 3: Closing the Loop & Launch Prep (Months 5-6) - IN PROGRESS ⏳

Goal: Build the critical feedback loop features, conduct thorough testing, and prepare the application for a public launch.

Sprint 8: Feedback Loop & Final UI (Weeks 15-16) - ✅ CURRENT SPRINT

Goal: To build the final core feature of the MVP: the automated feedback loop that closes the circuit on a user's action.

Step 45: Build the Bill Status Update Job (Backend)

Status: ✅ COMPLETE (Database schema portion completed during Sprint 6 remediation). The remaining work is to build the Lambda function itself.

Analysis: This is a crucial automated job that powers our entire feedback system. It needs to be reliable and efficient.

Instruction:

Create a new, scheduled AWS Lambda function.

The Lambda's handler function will:

Query our database for all bills in an "active" status.

For each bill, make an API call to the Open States API to get its latest status.

If the status has changed, create a new record in the existing bill_status_pipeline table.

Documentation: Scheduling AWS Lambda functions

Developer Log: (Awaiting developer update)

Step 46: Build the Vote Update Notification System (Backend)

Status: ⏳ TO DO

Analysis: Directly sending emails from the status-checking Lambda is brittle. A queue-based system is far more robust and scalable.

Instruction:

When the job in Step 45 detects a significant status change, it must publish a message to an AWS SQS (Simple Queue Service) queue. The message payload should be simple JSON, e.g., { "bill_id": "...", "new_status": "Passed", "vote_details": {...} }.

Create a second AWS Lambda function that is configured with the SQS queue as its event source.

This second Lambda's handler will be triggered automatically. It will parse the message, query the database for all users who acted on that bill, and call the EmailService to send a templated "Vote Update" email.

Documentation: Using Lambda with Amazon SQS

Developer Log: (Awaiting developer update)

Step 47: Implement the User Onboarding Flow (Frontend)

Status: ⏳ TO DO

Analysis: This is our chance to personalize the user's experience from their very first visit.

Instruction:

Create a new component: components/shared/OnboardingModal.tsx.

Use a client-side state management library (Zustand) to manage hasCompletedOnboarding and issuePreferences.

On the main layout, check if hasCompletedOnboarding is false and show the modal if so.

The modal should allow users to select issues. On save, update the Zustand store and persist it to localStorage.

Documentation: Zustand State Management

Developer Log: (Awaiting developer update)

Step 48: Feature & Testing Mandate

Status: ⏳ TO DO

Analysis: Testing this asynchronous, multi-service workflow is complex and requires robust mocking.

Instruction:

Backend (Integration Test): Write an integration test for the entire feedback loop using the moto library to mock SQS and SES. Test that the status checker Lambda correctly sends a message to the mock SQS queue, and that the notification Lambda is triggered by a mock SQS event and calls the mock SES service.

Frontend (E2E Test): Create a Playwright test for the onboarding flow. Use page.evaluate() to clear localStorage to simulate a first-time visit. Assert that the modal appears, can be interacted with, and correctly writes to localStorage on save.

Documentation: Moto Library for mocking AWS Services

Developer Log: (Awaiting developer update)

Sprint 9: Deployment & Infrastructure as Code (Weeks 17-18)

Goal: To containerize our applications and define our entire cloud infrastructure as code, preparing for scalable and repeatable deployments.

Step 49: Containerize Applications

Status: ⏳ TO DO

Instruction: Write optimized, multi-stage Dockerfiles for both the api and web applications to create lean, secure production images.

Developer Log: (Awaiting developer update)

Step 50: Define Infrastructure as Code (IaC)

Status: ⏳ TO DO

Instruction: Using the AWS CDK (TypeScript), define the entire cloud infrastructure: VPC, Fargate services, task definitions, load balancers, RDS instances, and Lambda functions. Commit this to Git to version-control our infrastructure.

Documentation: Getting started with the AWS CDK

Developer Log: (Awaiting developer update)

Step 51: Deploy to Staging Environment

Status: ⏳ TO DO

Instruction: Run the CDK script to deploy the entire application stack to a dedicated staging environment in AWS.

Developer Log: (Awaiting developer update)

Step 52: Configure DNS & HTTPS

Status: ⏳ TO DO

Instruction: In AWS Route 53, configure a staging subdomain to point to the staging load balancer. Use AWS Certificate Manager to provision and attach an SSL/TLS certificate.

Developer Log: (Awaiting developer update)

Sprint 10: Final Testing & Launch (Weeks 19-24)

Goal: To conduct final, large-scale testing and successfully launch the application to the public.

Step 53: Full End-to-End Regression Testing

Status: ⏳ TO DO

Instruction: Execute all end-to-end Playwright tests against the live staging environment. Manually go through every user story defined in the PRD.

Developer Log: (Awaiting developer update)

Step 54: Load Testing

Status: ⏳ TO DO

Instruction: Use a tool like k6 or Locust to run a load test against the staging environment. Define success criteria beforehand (e.g., average response time < 200ms). Simulate a realistic user flow and monitor for bottlenecks.

Documentation: k6 Scenarios

Developer Log: (Awaiting developer update)

Step 55: Security Audit

Status: ⏳ TO DO

Instruction: Conduct a final security review. Check all dependencies for known vulnerabilities (npm audit, poetry check). Review IAM policies for least-privilege access. Manually test for common vulnerabilities like those in the OWASP Top 10.

Developer Log: (Awaiting developer update)

Step 56: Code Freeze & Production Deployment

Status: ⏳ TO DO

Instruction: Freeze the main branch. Create a final release branch. Run the AWS CDK script to deploy the production environment.

Developer Log: (Awaiting developer update)

Step 57: Final Sanity Check & Launch

Status: ⏳ TO DO

Instruction: Perform a final sanity check on the live production environment. Point the production domain (modernaction.io) to the production load balancer. Monitor logs and dashboards closely. Celebrate!

Developer Log: (Awaiting developer update)
