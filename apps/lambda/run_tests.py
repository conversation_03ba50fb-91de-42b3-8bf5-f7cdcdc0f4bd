#!/usr/bin/env python3
"""
Test runner for Lambda functions.

This script runs all tests for both Lambda functions and provides
a comprehensive test report for the bill status notification system.
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def run_tests_for_lambda(lambda_name: str, lambda_path: Path) -> dict:
    """
    Run tests for a specific Lambda function.
    
    Args:
        lambda_name: Name of the Lambda function
        lambda_path: Path to the Lambda function directory
        
    Returns:
        Dict containing test results
    """
    print(f"\n{'='*60}")
    print(f"Running tests for {lambda_name}")
    print(f"{'='*60}")
    
    test_file = lambda_path / "test_lambda_integration.py"
    
    if not test_file.exists():
        print(f"❌ Test file not found: {test_file}")
        return {
            'lambda_name': lambda_name,
            'success': False,
            'error': f"Test file not found: {test_file}"
        }
    
    # Change to the Lambda directory
    original_cwd = os.getcwd()
    os.chdir(lambda_path)
    
    try:
        # Run pytest with verbose output
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "test_lambda_integration.py", 
            "-v", 
            "--tb=short",
            "--disable-warnings"
        ], capture_output=True, text=True)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        success = result.returncode == 0
        
        if success:
            print(f"✅ All tests passed for {lambda_name}")
        else:
            print(f"❌ Some tests failed for {lambda_name}")
        
        return {
            'lambda_name': lambda_name,
            'success': success,
            'return_code': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
    except Exception as e:
        print(f"❌ Error running tests for {lambda_name}: {e}")
        return {
            'lambda_name': lambda_name,
            'success': False,
            'error': str(e)
        }
    
    finally:
        os.chdir(original_cwd)


def test_local_lambda_execution():
    """Test local execution of Lambda functions with mock events"""
    print(f"\n{'='*60}")
    print("Testing Local Lambda Execution")
    print(f"{'='*60}")
    
    results = []
    
    # Test bill_status_update Lambda
    bill_status_path = Path("apps/lambda/bill_status_update")
    print(f"\n🧪 Testing {bill_status_path / 'handler.py'} local execution...")
    
    try:
        os.chdir(bill_status_path)
        
        # Create a minimal test event file
        test_event = {
            'version': '0',
            'id': 'test-event',
            'detail-type': 'Scheduled Event',
            'source': 'aws.events',
            'time': '2024-07-18T12:00:00Z',
            'detail': {}
        }
        
        with open('test_local_event.json', 'w') as f:
            json.dump(test_event, f)
        
        # Mock the database and external API calls for local testing
        with open('test_local.py', 'w') as f:
            f.write('''
import os
import json
from unittest.mock import patch, Mock

# Set test environment variables
os.environ.update({
    'OPEN_STATES_API_KEY': 'test-api-key',
    'AWS_REGION': 'us-east-1',
    'SQS_QUEUE_URL': 'https://sqs.us-east-1.amazonaws.com/123456789012/test-queue',
    'DB_HOST': 'localhost',
    'DB_PORT': '5432',
    'DB_NAME': 'test_db',
    'DB_USER': 'test_user',
    'DB_PASSWORD': 'test_password'
})

from handler import lambda_handler

def test_local_execution():
    # Mock database
    mock_db = Mock()
    mock_db.execute.return_value = Mock()
    mock_db.commit.return_value = None
    mock_db.close.return_value = None
    
    # Mock service
    with patch('shared.database.get_database_session', return_value=mock_db), \\
         patch('shared.bill_status_service.BillStatusUpdateService') as mock_service_class, \\
         patch('shared.notification_service.NotificationService') as mock_notification_class:
        
        mock_service = Mock()
        mock_service.update_all_active_bills.return_value = (3, 1)
        mock_service_class.return_value = mock_service
        
        mock_notification_service = Mock()
        mock_notification_class.return_value = mock_notification_service
        
        # Load test event
        with open('test_local_event.json', 'r') as f:
            event = json.load(f)
        
        # Mock context
        context = Mock()
        context.get_remaining_time_in_millis.return_value = 300000
        
        # Execute handler
        result = lambda_handler(event, context)
        
        print(f"Local test result: {json.dumps(result, indent=2)}")
        
        assert result['statusCode'] == 200
        assert 'total_checked' in result['body']
        print("✅ Local execution test passed")
        return True

if __name__ == '__main__':
    test_local_execution()
''')
        
        result = subprocess.run([sys.executable, "test_local.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Bill status update Lambda local execution successful")
            results.append(('bill_status_update_local', True, result.stdout))
        else:
            print("❌ Bill status update Lambda local execution failed")
            print(result.stderr)
            results.append(('bill_status_update_local', False, result.stderr))
        
        # Clean up
        if os.path.exists('test_local_event.json'):
            os.remove('test_local_event.json')
        if os.path.exists('test_local.py'):
            os.remove('test_local.py')
            
    except Exception as e:
        print(f"❌ Error testing bill_status_update local execution: {e}")
        results.append(('bill_status_update_local', False, str(e)))
    
    finally:
        os.chdir(Path.cwd().parent.parent.parent)
    
    # Test notification_sender Lambda
    notification_path = Path("apps/lambda/notification_sender")
    print(f"\n🧪 Testing {notification_path / 'handler.py'} local execution...")
    
    try:
        os.chdir(notification_path)
        
        # Use the existing test event
        result = subprocess.run([sys.executable, "handler.py", "test_event.json"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Notification sender Lambda local execution successful")
            results.append(('notification_sender_local', True, result.stdout))
        else:
            print("❌ Notification sender Lambda local execution failed")
            print(result.stderr)
            results.append(('notification_sender_local', False, result.stderr))
            
    except Exception as e:
        print(f"❌ Error testing notification_sender local execution: {e}")
        results.append(('notification_sender_local', False, str(e)))
    
    finally:
        os.chdir(Path.cwd().parent.parent.parent)
    
    return results


def main():
    """Main test runner function"""
    print("🚀 Starting Lambda Functions Test Suite")
    print("="*60)
    
    # Change to the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent.parent
    os.chdir(project_root)
    
    test_results = []
    
    # Define Lambda functions to test
    lambda_functions = [
        ("bill_status_update", Path("apps/lambda/bill_status_update")),
        ("notification_sender", Path("apps/lambda/notification_sender"))
    ]
    
    # Run integration tests for each Lambda
    for lambda_name, lambda_path in lambda_functions:
        if lambda_path.exists():
            result = run_tests_for_lambda(lambda_name, lambda_path)
            test_results.append(result)
        else:
            print(f"❌ Lambda directory not found: {lambda_path}")
            test_results.append({
                'lambda_name': lambda_name,
                'success': False,
                'error': f"Directory not found: {lambda_path}"
            })
    
    # Test local execution
    local_results = test_local_lambda_execution()
    
    # Print summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result['success'])
    
    print(f"Integration Tests: {passed_tests}/{total_tests} Lambda functions passed")
    
    for result in test_results:
        status = "✅ PASSED" if result['success'] else "❌ FAILED"
        print(f"  {result['lambda_name']}: {status}")
        if not result['success'] and 'error' in result:
            print(f"    Error: {result['error']}")
    
    print(f"\nLocal Execution Tests:")
    local_passed = sum(1 for _, success, _ in local_results if success)
    print(f"  {local_passed}/{len(local_results)} local tests passed")
    
    for test_name, success, output in local_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    # Overall result
    all_passed = passed_tests == total_tests and local_passed == len(local_results)
    
    if all_passed:
        print(f"\n🎉 ALL TESTS PASSED! The bill status notification system is ready for deployment.")
        return 0
    else:
        print(f"\n❌ Some tests failed. Please review the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())