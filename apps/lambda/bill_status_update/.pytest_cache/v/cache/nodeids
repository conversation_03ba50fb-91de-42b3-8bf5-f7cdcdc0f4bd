["test_lambda.py::TestLambdaEnvironment::test_lambda_memory_configuration", "test_lambda.py::TestLambdaEnvironment::test_lambda_timeout_configuration", "test_lambda.py::TestLambdaEnvironment::test_required_environment_variables", "test_lambda.py::TestLambdaHandler::test_lambda_handler_database_connection_failure", "test_lambda.py::TestLambdaHandler::test_lambda_handler_missing_environment_variables", "test_lambda.py::TestLambdaHandler::test_lambda_handler_no_bills_to_update", "test_lambda.py::TestLambdaHandler::test_lambda_handler_partial_success", "test_lambda.py::TestLambdaHandler::test_lambda_handler_service_exception", "test_lambda.py::TestLambdaHandler::test_lambda_handler_success", "test_lambda_integration.py::TestBillStatusUpdateLambda::test_lambda_handler_database_error", "test_lambda_integration.py::TestBillStatusUpdateLambda::test_lambda_handler_service_error", "test_lambda_integration.py::TestBillStatusUpdateLambda::test_lambda_handler_success", "test_lambda_integration.py::TestBillStatusUpdateService::test_fetch_bill_status_failure", "test_lambda_integration.py::TestBillStatusUpdateService::test_fetch_bill_status_success", "test_lambda_integration.py::TestBillStatusUpdateService::test_is_significant_status_change", "test_lambda_integration.py::TestBillStatusUpdateService::test_map_openstates_status_to_bill_status", "test_lambda_integration.py::TestBillStatusUpdateService::test_service_initialization", "test_lambda_integration.py::TestBillStatusUpdateService::test_service_initialization_without_api_key", "test_lambda_integration.py::TestNotificationService::test_notification_service_initialization", "test_lambda_integration.py::TestNotificationService::test_notification_service_missing_queue_url", "test_lambda_integration.py::TestNotificationService::test_publish_non_significant_change", "test_lambda_integration.py::TestNotificationService::test_publish_status_change_success"]