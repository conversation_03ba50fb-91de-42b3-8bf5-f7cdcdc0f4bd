# Environment variables for local Lambda testing
# Copy this file to .env and fill in your values

# OpenStates API Key (required)
OPEN_STATES_API_KEY=your_openstates_api_key_here

# Database connection (choose one approach)

# Option 1: Full database URL
DATABASE_URL=postgresql://user:password@localhost:5432/modernaction_test

# Option 2: Individual components
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=modernaction_test
# DB_USER=your_db_user
# DB_PASSWORD=your_db_password

# AWS Region (optional, defaults to us-east-1)
AWS_REGION=us-east-1
