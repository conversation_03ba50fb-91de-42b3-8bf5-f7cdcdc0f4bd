"""
AWS Lambda function for updating bill statuses.

This function runs on a schedule (e.g., daily) to check for bill status updates
from the OpenStates API and update our database accordingly.
"""

import json
import logging
import os
import sys
from typing import Dict, Any

# Load environment variables from .env file for local testing
if os.path.exists(os.path.join(os.path.dirname(__file__), '.env')):
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))

# Add the shared code to the path
sys.path.append('/opt/python')
sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler for bill status updates.
    
    Args:
        event: Lambda event data (from EventBridge schedule)
        context: Lambda context object
        
    Returns:
        Dict with execution results
    """
    logger.info("Starting bill status update job")
    
    try:
        # Import here to avoid cold start issues
        from shared.database import get_database_session
        from shared.bill_status_service import BillStatusUpdateService
        from shared.notification_service import NotificationService
        
        # Get database session
        db = get_database_session()
        if not db:
            raise Exception("Could not establish database connection")
        
        try:
            # Create notification service for SQS publishing
            notification_service = NotificationService()
            
            # Create service instance with notification service
            service = BillStatusUpdateService(db, notification_service)
            
            # Update all active bills
            total_checked, total_updated = service.update_all_active_bills()
            
            # Commit any remaining changes
            db.commit()
            
            # Prepare response
            result = {
                'statusCode': 200,
                'body': {
                    'message': 'Bill status update completed successfully',
                    'total_checked': total_checked,
                    'total_updated': total_updated,
                    'execution_time': context.get_remaining_time_in_millis() if context else None
                }
            }
            
            logger.info(f"Bill status update completed: {total_updated}/{total_checked} bills updated")
            return result
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Bill status update job failed: {str(e)}", exc_info=True)
        
        # Return error response
        return {
            'statusCode': 500,
            'body': {
                'message': 'Bill status update failed',
                'error': str(e)
            }
        }


def local_test(test_event_file: str = None):
    """
    Function for local testing of the Lambda handler.

    Args:
        test_event_file: Optional path to JSON file containing test event
    """
    print("Testing bill status update Lambda function locally...")

    # Load test event from file if provided
    if test_event_file and os.path.exists(test_event_file):
        with open(test_event_file, 'r') as f:
            event = json.load(f)
        print(f"Loaded test event from {test_event_file}")
    else:
        # Default mock event
        event = {
            'version': '0',
            'id': 'test-event-id',
            'detail-type': 'Scheduled Event',
            'source': 'aws.events',
            'account': '************',
            'time': '2024-07-18T12:00:00Z',
            'region': 'us-east-1',
            'detail': {},
            'resources': ['arn:aws:events:us-east-1:************:rule/bill-status-update-schedule']
        }

    class MockContext:
        def __init__(self):
            self.function_name = 'modernaction-bill-status-update'
            self.function_version = '$LATEST'
            self.invoked_function_arn = 'arn:aws:lambda:us-east-1:************:function:modernaction-bill-status-update'
            self.memory_limit_in_mb = '512'
            self.remaining_time_in_millis = 300000
            self.log_group_name = '/aws/lambda/modernaction-bill-status-update'
            self.log_stream_name = '2024/07/18/[$LATEST]test'
            self.aws_request_id = 'test-request-id'

        def get_remaining_time_in_millis(self):
            return self.remaining_time_in_millis

    context = MockContext()

    # Run the handler
    print("Executing Lambda handler...")
    result = lambda_handler(event, context)
    print(f"Result: {json.dumps(result, indent=2)}")

    return result


if __name__ == "__main__":
    import sys
    test_event_file = sys.argv[1] if len(sys.argv) > 1 else None
    local_test(test_event_file)
