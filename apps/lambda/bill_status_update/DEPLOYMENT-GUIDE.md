# Bill Status Update Lambda - Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Bill Status Update Lambda function using AWS CDK, manual deployment, or local testing.

## Prerequisites

### Required Tools
- AWS CLI configured with appropriate permissions
- AWS CDK v2 installed (`npm install -g aws-cdk`)
- Python 3.11+ with Poetry
- Docker (optional, for local testing)

### Required AWS Permissions
Your AWS user/role needs permissions for:
- Lambda function management
- EventBridge rule creation
- SQS queue management
- Secrets Manager access
- RDS connectivity
- VPC and Security Group management
- CloudWatch Logs access

### Required Secrets
Before deployment, create these secrets in AWS Secrets Manager:

```bash
# OpenStates API Key
aws secretsmanager create-secret \
  --name "modernaction/openstates/api_key" \
  --description "OpenStates API key for bill status updates" \
  --secret-string "your_openstates_api_key_here"
```

## Deployment Methods

### Method 1: CDK Deployment (Recommended)

This is the recommended approach for production deployments.

#### Step 1: Prepare Infrastructure

```bash
cd infrastructure
npm install
```

#### Step 2: Configure Environment

```bash
# Set environment variables
export CDK_DEFAULT_ACCOUNT=************
export CDK_DEFAULT_REGION=us-east-1
export ENVIRONMENT=dev  # or staging, prod
```

#### Step 3: Bootstrap CDK (First Time Only)

```bash
cdk bootstrap
```

#### Step 4: Deploy Stack

```bash
# Preview changes
cdk diff

# Deploy infrastructure
cdk deploy

# Confirm deployment when prompted
```

#### Step 5: Verify Deployment

```bash
# Check Lambda function
aws lambda get-function --function-name modernaction-bill-status-update-dev

# Check EventBridge rule
aws events list-rules --name-prefix modernaction-bill-status-update

# Check SQS queue
aws sqs list-queues | grep bill-status-notifications
```

### Method 2: Manual Deployment

Use this method for development or when CDK is not available.

#### Step 1: Build Deployment Package

```bash
cd apps/lambda/bill_status_update
./deploy.sh
```

#### Step 2: Create Lambda Function

```bash
# Create execution role
aws iam create-role \
  --role-name modernaction-bill-status-lambda-role \
  --assume-role-policy-document '{
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Principal": {"Service": "lambda.amazonaws.com"},
        "Action": "sts:AssumeRole"
      }
    ]
  }'

# Attach basic execution policy
aws iam attach-role-policy \
  --role-name modernaction-bill-status-lambda-role \
  --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

# Create Lambda function
aws lambda create-function \
  --function-name modernaction-bill-status-update \
  --runtime python3.11 \
  --role arn:aws:iam::ACCOUNT:role/modernaction-bill-status-lambda-role \
  --handler handler.lambda_handler \
  --zip-file fileb://build/lambda-package.zip \
  --timeout 900 \
  --memory-size 512
```

#### Step 3: Configure Environment Variables

```bash
aws lambda update-function-configuration \
  --function-name modernaction-bill-status-update \
  --environment Variables='{
    "OPEN_STATES_API_KEY":"your-api-key",
    "DB_HOST":"your-db-host",
    "DB_PORT":"5432",
    "DB_NAME":"modernaction",
    "DB_USER":"your-db-user",
    "DB_PASSWORD":"your-db-password",
    "AWS_REGION":"us-east-1"
  }'
```

#### Step 4: Create EventBridge Schedule

```bash
# Create EventBridge rule
aws events put-rule \
  --name modernaction-bill-status-update-schedule \
  --schedule-expression "rate(1 day)" \
  --description "Daily trigger for bill status updates"

# Add Lambda as target
aws events put-targets \
  --rule modernaction-bill-status-update-schedule \
  --targets "Id"="1","Arn"="arn:aws:lambda:REGION:ACCOUNT:function:modernaction-bill-status-update"

# Grant EventBridge permission to invoke Lambda
aws lambda add-permission \
  --function-name modernaction-bill-status-update \
  --statement-id allow-eventbridge \
  --action lambda:InvokeFunction \
  --principal events.amazonaws.com \
  --source-arn arn:aws:events:REGION:ACCOUNT:rule/modernaction-bill-status-update-schedule
```

### Method 3: Local Testing

For development and testing purposes.

#### Step 1: Set Up Local Environment

```bash
cd apps/lambda/bill_status_update

# Copy environment template
cp .env.example .env

# Edit .env with your values
nano .env
```

#### Step 2: Install Dependencies

```bash
pip install -r requirements.txt
```

#### Step 3: Run Local Test

```bash
# Basic test
python handler.py

# Test with custom event
python handler.py test_event.json
```

## Configuration

### Environment Variables

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `OPEN_STATES_API_KEY` | Yes | OpenStates API key | `abc123...` |
| `DATABASE_URL` | No* | Full database URL | `********************************/db` |
| `DB_HOST` | No* | Database host | `localhost` |
| `DB_PORT` | No* | Database port | `5432` |
| `DB_NAME` | No* | Database name | `modernaction` |
| `DB_USER` | No* | Database user | `postgres` |
| `DB_PASSWORD` | No* | Database password | `password` |
| `AWS_REGION` | No | AWS region | `us-east-1` |
| `SQS_QUEUE_URL` | No | SQS queue URL | `https://sqs...` |

*Either `DATABASE_URL` or all individual `DB_*` variables are required.

### Lambda Configuration

| Setting | Recommended Value | Description |
|---------|------------------|-------------|
| Runtime | `python3.11` | Python runtime version |
| Memory | `512 MB` | Memory allocation |
| Timeout | `15 minutes` | Maximum execution time |
| Architecture | `x86_64` | Processor architecture |

### EventBridge Schedule

| Setting | Value | Description |
|---------|-------|-------------|
| Schedule | `rate(1 day)` | Run once daily |
| State | `ENABLED` | Active scheduling |
| Target | Lambda function | Invoke our function |

## Monitoring

### CloudWatch Dashboards

Create a dashboard to monitor:
- Lambda invocations and errors
- Execution duration
- Memory utilization
- Database connection metrics

### CloudWatch Alarms

Set up alarms for:
- Lambda function errors
- Execution timeouts
- No successful executions in 25 hours
- High memory usage

### Log Analysis

Key log patterns to monitor:
```bash
# Successful executions
aws logs filter-log-events \
  --log-group-name /aws/lambda/modernaction-bill-status-update \
  --filter-pattern "Bill status update complete"

# Errors
aws logs filter-log-events \
  --log-group-name /aws/lambda/modernaction-bill-status-update \
  --filter-pattern "ERROR"
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Failures
```
Error: connection to server at "host" failed: Operation timed out
```

**Solutions:**
- Check VPC configuration
- Verify security group rules
- Confirm RDS instance is running
- Test database connectivity from Lambda subnet

#### 2. OpenStates API Errors
```
Error: Failed to fetch bill from OpenStates API: 401 Unauthorized
```

**Solutions:**
- Verify API key in Secrets Manager
- Check API key permissions
- Review OpenStates API documentation for changes

#### 3. Lambda Timeout
```
Task timed out after 900.00 seconds
```

**Solutions:**
- Increase Lambda timeout
- Optimize database queries
- Reduce number of bills processed per execution

#### 4. Memory Issues
```
Runtime.OutOfMemoryError: Memory limit exceeded
```

**Solutions:**
- Increase Lambda memory allocation
- Optimize data structures
- Process bills in smaller batches

### Debug Commands

```bash
# View recent logs
aws logs tail /aws/lambda/modernaction-bill-status-update --follow

# Test function manually
aws lambda invoke \
  --function-name modernaction-bill-status-update \
  --payload '{"test": true}' \
  response.json

# Check function configuration
aws lambda get-function-configuration \
  --function-name modernaction-bill-status-update
```

## Updates and Maintenance

### Updating Function Code

```bash
# CDK deployment
cd infrastructure
cdk deploy

# Manual deployment
cd apps/lambda/bill_status_update
./deploy.sh
aws lambda update-function-code \
  --function-name modernaction-bill-status-update \
  --zip-file fileb://build/lambda-package.zip
```

### Updating Configuration

```bash
# Update environment variables
aws lambda update-function-configuration \
  --function-name modernaction-bill-status-update \
  --environment Variables='{...}'

# Update timeout
aws lambda update-function-configuration \
  --function-name modernaction-bill-status-update \
  --timeout 1200
```

### Monitoring Health

```bash
# Check recent invocations
aws lambda get-function \
  --function-name modernaction-bill-status-update \
  --query 'Configuration.[LastModified,State,StateReason]'

# View metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name Invocations \
  --dimensions Name=FunctionName,Value=modernaction-bill-status-update \
  --start-time 2024-07-17T00:00:00Z \
  --end-time 2024-07-18T00:00:00Z \
  --period 3600 \
  --statistics Sum
```

## Security Best Practices

1. **Use Secrets Manager** for all sensitive configuration
2. **Minimal IAM permissions** - only what's needed
3. **VPC deployment** for database security
4. **Encrypted environment variables** when possible
5. **Regular security updates** for dependencies
6. **Monitor access patterns** in CloudTrail

## Cost Optimization

1. **Right-size memory** allocation based on actual usage
2. **Monitor execution duration** and optimize code
3. **Use provisioned concurrency** only if needed
4. **Review CloudWatch log retention** settings
5. **Consider Reserved Capacity** for predictable workloads

---

This deployment guide ensures reliable and secure deployment of the Bill Status Update Lambda function.
