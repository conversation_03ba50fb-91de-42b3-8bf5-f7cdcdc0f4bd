"""
Notification service for publishing bill status change messages to SQS.
"""

import json
import logging
import os
from typing import Dict, Any, Optional
import boto3
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for publishing bill status change notifications to SQS"""
    
    def __init__(self):
        self.sqs_client = boto3.client('sqs', region_name=os.getenv('AWS_REGION', 'us-east-1'))
        self.queue_url = os.getenv('SQS_QUEUE_URL')
        
        if not self.queue_url:
            raise ValueError("SQS_QUEUE_URL environment variable is required")
    
    def publish_status_change(self, status_change: Dict[str, Any]) -> bool:
        """
        Publish a bill status change notification to SQS.
        
        Args:
            status_change: Dictionary containing status change details
            
        Returns:
            True if message was published successfully
        """
        try:
            # Prepare the message payload
            message_body = {
                'event_type': 'bill_status_change',
                'bill_id': status_change['bill_id'],
                'previous_status': status_change['previous_status'],
                'current_status': status_change['current_status'],
                'is_significant_change': status_change['is_significant_change'],
                'vote_details': status_change.get('vote_details'),
                'timestamp': status_change.get('timestamp')
            }
            
            # Only publish if it's a significant change
            if not status_change['is_significant_change']:
                logger.info(f"Skipping notification for non-significant change: bill {status_change['bill_id']}")
                return True
            
            # Send message to SQS
            response = self.sqs_client.send_message(
                QueueUrl=self.queue_url,
                MessageBody=json.dumps(message_body),
                MessageAttributes={
                    'bill_id': {
                        'StringValue': str(status_change['bill_id']),
                        'DataType': 'String'
                    },
                    'event_type': {
                        'StringValue': 'bill_status_change',
                        'DataType': 'String'
                    },
                    'status_change': {
                        'StringValue': f"{status_change['previous_status']}->{status_change['current_status']}",
                        'DataType': 'String'
                    }
                }
            )
            
            logger.info(f"Published bill status change notification: {response['MessageId']} for bill {status_change['bill_id']}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to publish notification to SQS: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error publishing notification: {e}")
            return False