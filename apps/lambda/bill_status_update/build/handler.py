"""
AWS Lambda function for updating bill statuses.

This function runs on a schedule (e.g., daily) to check for bill status updates
from the OpenStates API and update our database accordingly.
"""

import json
import logging
import os
import sys
from typing import Dict, Any

# Add the shared code to the path
sys.path.append('/opt/python')
sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler for bill status updates.
    
    Args:
        event: Lambda event data (from EventBridge schedule)
        context: Lambda context object
        
    Returns:
        Dict with execution results
    """
    logger.info("Starting bill status update job")
    
    try:
        # Import here to avoid cold start issues
        from shared.database import get_database_session
        from shared.bill_status_service import BillStatusUpdateService
        
        # Get database session
        db = get_database_session()
        if not db:
            raise Exception("Could not establish database connection")
        
        try:
            # Create service instance
            service = BillStatusUpdateService(db)
            
            # Update all active bills
            total_checked, total_updated = service.update_all_active_bills()
            
            # Commit any remaining changes
            db.commit()
            
            # Prepare response
            result = {
                'statusCode': 200,
                'body': {
                    'message': 'Bill status update completed successfully',
                    'total_checked': total_checked,
                    'total_updated': total_updated,
                    'execution_time': context.get_remaining_time_in_millis() if context else None
                }
            }
            
            logger.info(f"Bill status update completed: {total_updated}/{total_checked} bills updated")
            return result
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Bill status update job failed: {str(e)}", exc_info=True)
        
        # Return error response
        return {
            'statusCode': 500,
            'body': {
                'message': 'Bill status update failed',
                'error': str(e)
            }
        }


def local_test():
    """
    Function for local testing of the Lambda handler.
    """
    print("Testing bill status update Lambda function locally...")
    
    # Mock event and context
    event = {
        'source': 'aws.events',
        'detail-type': 'Scheduled Event',
        'detail': {}
    }
    
    class MockContext:
        def get_remaining_time_in_millis(self):
            return 300000  # 5 minutes
    
    context = MockContext()
    
    # Run the handler
    result = lambda_handler(event, context)
    print(f"Result: {json.dumps(result, indent=2)}")


if __name__ == "__main__":
    local_test()
