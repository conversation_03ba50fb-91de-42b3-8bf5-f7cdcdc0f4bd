"""
Integration tests for the bill status update Lambda function.

These tests use moto to mock AWS services and verify the complete
end-to-end functionality including SQS message publishing.
"""

import json
import os
import pytest
from unittest.mock import Mock, patch, MagicMock
from moto import mock_sqs
import boto3

# Set environment variables for testing
os.environ.update({
    'OPEN_STATES_API_KEY': 'test-api-key',
    'AWS_REGION': 'us-east-1',
    'SQS_QUEUE_URL': 'https://sqs.us-east-1.amazonaws.com/123456789012/test-queue',
    'DB_HOST': 'localhost',
    'DB_PORT': '5432',
    'DB_NAME': 'test_db',
    'DB_USER': 'test_user',
    'DB_PASSWORD': 'test_password'
})

# Import the handler and services after setting environment variables
from handler import lambda_handler
from shared.bill_status_service import BillStatusUpdateService
from shared.notification_service import NotificationService


class TestBillStatusUpdateLambda:
    """Test suite for the bill status update Lambda function"""
    
    @mock_sqs
    def test_lambda_handler_success(self):
        """Test successful Lambda execution with SQS publishing"""
        # Create mock SQS queue
        sqs_client = boto3.client('sqs', region_name='us-east-1')
        queue_url = sqs_client.create_queue(QueueName='test-queue')['QueueUrl']
        
        # Mock database session
        mock_db = Mock()
        mock_db.execute.return_value = Mock()
        mock_db.commit.return_value = None
        mock_db.close.return_value = None
        
        # Mock the service to return successful updates
        with patch('shared.database.get_database_session', return_value=mock_db), \
             patch('shared.bill_status_service.BillStatusUpdateService') as mock_service_class, \
             patch('shared.notification_service.NotificationService') as mock_notification_class:
            
            # Configure mocks
            mock_service = Mock()
            mock_service.update_all_active_bills.return_value = (5, 2)  # 5 checked, 2 updated
            mock_service_class.return_value = mock_service
            
            mock_notification_service = Mock()
            mock_notification_class.return_value = mock_notification_service
            
            # Create test event and context
            event = {}
            context = Mock()
            context.get_remaining_time_in_millis.return_value = 300000
            
            # Execute handler
            result = lambda_handler(event, context)
            
            # Verify response
            assert result['statusCode'] == 200
            assert result['body']['total_checked'] == 5
            assert result['body']['total_updated'] == 2
            assert 'Bill status update completed successfully' in result['body']['message']
            
            # Verify service was called correctly
            mock_service_class.assert_called_once_with(mock_db, mock_notification_service)
            mock_service.update_all_active_bills.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.close.assert_called_once()
    
    @mock_sqs
    def test_lambda_handler_database_error(self):
        """Test Lambda handler with database connection failure"""
        with patch('shared.database.get_database_session', return_value=None):
            event = {}
            context = Mock()
            
            result = lambda_handler(event, context)
            
            assert result['statusCode'] == 500
            assert 'Could not establish database connection' in result['body']['error']
    
    @mock_sqs
    def test_lambda_handler_service_error(self):
        """Test Lambda handler with service error"""
        mock_db = Mock()
        mock_db.close.return_value = None
        
        with patch('shared.database.get_database_session', return_value=mock_db), \
             patch('shared.bill_status_service.BillStatusUpdateService', side_effect=Exception("Service error")):
            
            event = {}
            context = Mock()
            
            result = lambda_handler(event, context)
            
            assert result['statusCode'] == 500
            assert 'Service error' in result['body']['error']
            mock_db.close.assert_called_once()


class TestBillStatusUpdateService:
    """Test suite for the BillStatusUpdateService"""
    
    def test_service_initialization(self):
        """Test service initialization with notification service"""
        mock_db = Mock()
        mock_notification_service = Mock()
        
        service = BillStatusUpdateService(mock_db, mock_notification_service)
        
        assert service.db == mock_db
        assert service.notification_service == mock_notification_service
        assert service.api_key == 'test-api-key'
    
    def test_service_initialization_without_api_key(self):
        """Test service initialization fails without API key"""
        mock_db = Mock()
        
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="OPEN_STATES_API_KEY environment variable is required"):
                BillStatusUpdateService(mock_db)
    
    @patch('shared.bill_status_service.requests.get')
    def test_fetch_bill_status_success(self, mock_get):
        """Test successful bill status fetch from OpenStates API"""
        mock_db = Mock()
        service = BillStatusUpdateService(mock_db)
        
        # Mock successful API response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {'id': 'test-bill', 'actions': []}
        mock_get.return_value = mock_response
        
        result = service.fetch_bill_status_from_openstates('test-bill-id')
        
        assert result == {'id': 'test-bill', 'actions': []}
        mock_get.assert_called_once()
        
        # Verify API call parameters
        call_args = mock_get.call_args
        assert 'https://v3.openstates.org/bills/test-bill-id' in call_args[0]
        assert call_args[1]['headers']['X-API-KEY'] == 'test-api-key'
    
    @patch('shared.bill_status_service.requests.get')
    def test_fetch_bill_status_failure(self, mock_get):
        """Test bill status fetch failure"""
        mock_db = Mock()
        service = BillStatusUpdateService(mock_db)
        
        # Mock failed API response - use requests.exceptions.RequestException for more realistic testing
        from requests.exceptions import RequestException
        mock_get.side_effect = RequestException("API Error")
        
        result = service.fetch_bill_status_from_openstates('test-bill-id')
        
        assert result is None
    
    def test_is_significant_status_change(self):
        """Test significant status change detection"""
        mock_db = Mock()
        service = BillStatusUpdateService(mock_db)
        
        # Test significant changes
        assert service.is_significant_status_change('draft', 'introduced') is True
        assert service.is_significant_status_change('committee', 'floor') is True
        assert service.is_significant_status_change('floor', 'passed') is True
        assert service.is_significant_status_change('introduced', 'signed') is True
        
        # Test non-significant changes
        assert service.is_significant_status_change('introduced', 'introduced') is False
        
        # Test final statuses are always significant
        assert service.is_significant_status_change('floor', 'passed') is True
        assert service.is_significant_status_change('floor', 'signed') is True
        assert service.is_significant_status_change('floor', 'vetoed') is True
        assert service.is_significant_status_change('floor', 'failed') is True
    
    def test_map_openstates_status_to_bill_status(self):
        """Test status mapping from OpenStates format"""
        mock_db = Mock()
        service = BillStatusUpdateService(mock_db)
        
        # Test signed status
        openstates_data = {
            'actions': [{'description': 'Bill signed by governor'}]
        }
        assert service.map_openstates_status_to_bill_status(openstates_data) == 'signed'
        
        # Test vetoed status
        openstates_data = {
            'actions': [{'description': 'Bill vetoed by governor'}]
        }
        assert service.map_openstates_status_to_bill_status(openstates_data) == 'vetoed'
        
        # Test passed status
        openstates_data = {
            'actions': [{'description': 'Passed final reading'}]
        }
        assert service.map_openstates_status_to_bill_status(openstates_data) == 'passed'
        
        # Test failed status
        openstates_data = {
            'actions': [{'description': 'Bill failed in committee'}]
        }
        assert service.map_openstates_status_to_bill_status(openstates_data) == 'failed'
        
        # Test floor status
        openstates_data = {
            'actions': [{'description': 'Bill moved to floor for consideration'}]
        }
        assert service.map_openstates_status_to_bill_status(openstates_data) == 'floor'
        
        # Test committee status
        openstates_data = {
            'actions': [{'description': 'Referred to committee'}]
        }
        assert service.map_openstates_status_to_bill_status(openstates_data) == 'committee'
        
        # Test default introduced status
        openstates_data = {
            'actions': [{'description': 'Bill introduced'}]
        }
        assert service.map_openstates_status_to_bill_status(openstates_data) == 'introduced'


class TestNotificationService:
    """Test suite for the NotificationService"""
    
    @mock_sqs
    def test_notification_service_initialization(self):
        """Test notification service initialization"""
        service = NotificationService()
        assert service.queue_url == 'https://sqs.us-east-1.amazonaws.com/123456789012/test-queue'
    
    def test_notification_service_missing_queue_url(self):
        """Test notification service fails without queue URL"""
        with patch.dict(os.environ, {'SQS_QUEUE_URL': ''}, clear=False):
            with pytest.raises(ValueError, match="SQS_QUEUE_URL environment variable is required"):
                NotificationService()
    
    @mock_sqs
    def test_publish_status_change_success(self):
        """Test successful SQS message publishing"""
        # Create mock SQS queue
        sqs_client = boto3.client('sqs', region_name='us-east-1')
        queue_url = sqs_client.create_queue(QueueName='test-queue')['QueueUrl']
        
        # Update environment with real queue URL
        with patch.dict(os.environ, {'SQS_QUEUE_URL': queue_url}):
            service = NotificationService()
            
            status_change = {
                'bill_id': 123,
                'previous_status': 'committee',
                'current_status': 'floor',
                'is_significant_change': True,
                'vote_details': {'action_description': 'Test vote'},
                'timestamp': '2024-07-18T12:00:00Z'
            }
            
            result = service.publish_status_change(status_change)
            
            assert result is True
            
            # Verify message was sent to queue
            messages = sqs_client.receive_message(QueueUrl=queue_url)
            assert 'Messages' in messages
            
            message_body = json.loads(messages['Messages'][0]['Body'])
            assert message_body['bill_id'] == 123
            assert message_body['current_status'] == 'floor'
            assert message_body['event_type'] == 'bill_status_change'
    
    @mock_sqs
    def test_publish_non_significant_change(self):
        """Test that non-significant changes are not published"""
        sqs_client = boto3.client('sqs', region_name='us-east-1')
        queue_url = sqs_client.create_queue(QueueName='test-queue')['QueueUrl']
        
        with patch.dict(os.environ, {'SQS_QUEUE_URL': queue_url}):
            service = NotificationService()
            
            status_change = {
                'bill_id': 123,
                'previous_status': 'committee',
                'current_status': 'committee',
                'is_significant_change': False,
                'vote_details': None,
                'timestamp': '2024-07-18T12:00:00Z'
            }
            
            result = service.publish_status_change(status_change)
            
            assert result is True  # Returns True but doesn't publish
            
            # Verify no message was sent
            messages = sqs_client.receive_message(QueueUrl=queue_url)
            assert 'Messages' not in messages


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])