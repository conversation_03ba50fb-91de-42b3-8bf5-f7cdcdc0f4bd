["test_lambda_integration.py::TestDatabaseFunctions::test_get_bill_data", "test_lambda_integration.py::TestDatabaseFunctions::test_get_bill_data_not_found", "test_lambda_integration.py::TestDatabaseFunctions::test_get_user_actions_for_bill", "test_lambda_integration.py::TestEmailService::test_email_service_initialization", "test_lambda_integration.py::TestEmailService::test_generate_email_subject", "test_lambda_integration.py::TestEmailService::test_generate_html_email_body", "test_lambda_integration.py::TestEmailService::test_generate_text_email_body", "test_lambda_integration.py::TestEmailService::test_send_vote_update_email_success", "test_lambda_integration.py::TestNotificationSenderLambda::test_lambda_handler_database_error", "test_lambda_integration.py::TestNotificationSenderLambda::test_lambda_handler_empty_event", "test_lambda_integration.py::TestNotificationSenderLambda::test_lambda_handler_multiple_messages", "test_lambda_integration.py::TestNotificationSenderLambda::test_lambda_handler_success", "test_lambda_integration.py::TestProcessBillNotification::test_process_bill_notification_bill_not_found", "test_lambda_integration.py::TestProcessBillNotification::test_process_bill_notification_missing_bill_id", "test_lambda_integration.py::TestProcessBillNotification::test_process_bill_notification_no_user_actions", "test_lambda_integration.py::TestProcessBillNotification::test_process_bill_notification_success"]