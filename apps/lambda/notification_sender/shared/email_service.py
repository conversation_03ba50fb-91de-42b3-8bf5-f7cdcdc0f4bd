"""
Email service for sending bill status update notifications.

This service handles sending vote update emails to users who have
taken actions on bills that have had status changes.
"""

import logging
import os
from typing import Dict, Any, List, Optional
import boto3
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending bill status update emails via SES"""
    
    def __init__(self):
        self.ses_client = boto3.client('ses', region_name=os.getenv('AWS_REGION', 'us-east-1'))
        self.from_email = os.getenv('FROM_EMAIL', '<EMAIL>')
        self.reply_to_email = os.getenv('REPLY_TO_EMAIL', '<EMAIL>')
    
    def send_vote_update_email(
        self, 
        to_email: str, 
        bill_data: Dict[str, Any], 
        status_change: Dict[str, Any],
        user_action: Dict[str, Any]
    ) -> bool:
        """
        Send a vote update email to a user.
        
        Args:
            to_email: Recipient email address
            bill_data: Bill information
            status_change: Status change details
            user_action: User's original action details
            
        Returns:
            True if email sent successfully
        """
        try:
            # Generate email content
            subject = self._generate_email_subject(bill_data, status_change)
            html_body = self._generate_html_email_body(bill_data, status_change, user_action)
            text_body = self._generate_text_email_body(bill_data, status_change, user_action)
            
            # Send email via SES
            response = self.ses_client.send_email(
                Source=self.from_email,
                Destination={
                    'ToAddresses': [to_email]
                },
                Message={
                    'Subject': {
                        'Data': subject,
                        'Charset': 'UTF-8'
                    },
                    'Body': {
                        'Html': {
                            'Data': html_body,
                            'Charset': 'UTF-8'
                        },
                        'Text': {
                            'Data': text_body,
                            'Charset': 'UTF-8'
                        }
                    }
                },
                ReplyToAddresses=[self.reply_to_email]
            )
            
            logger.info(f"Vote update email sent successfully: {response['MessageId']} to {to_email}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to send vote update email to {to_email}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending vote update email to {to_email}: {e}")
            return False
    
    def _generate_email_subject(self, bill_data: Dict[str, Any], status_change: Dict[str, Any]) -> str:
        """Generate email subject line"""
        bill_number = bill_data.get('bill_number', 'Unknown Bill')
        new_status = status_change['current_status']
        
        status_messages = {
            'committee': 'moved to committee',
            'floor': 'reached the floor',
            'passed': 'passed',
            'signed': 'was signed into law',
            'vetoed': 'was vetoed',
            'failed': 'failed'
        }
        
        status_message = status_messages.get(new_status, f'status changed to {new_status}')
        
        return f"Update: {bill_number} {status_message}"
    
    def _generate_html_email_body(
        self, 
        bill_data: Dict[str, Any], 
        status_change: Dict[str, Any],
        user_action: Dict[str, Any]
    ) -> str:
        """Generate HTML email body"""
        bill_number = bill_data.get('bill_number', 'Unknown Bill')
        bill_title = bill_data.get('title', 'Unknown Title')
        previous_status = status_change['previous_status']
        current_status = status_change['current_status']
        vote_details = status_change.get('vote_details', {})
        
        # Format vote details if available
        vote_info = ""
        if vote_details:
            vote_info = f"""
            <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0;">
                <h4 style="margin: 0 0 10px 0; color: #333;">Vote Details</h4>
                <p style="margin: 5px 0;"><strong>Action:</strong> {vote_details.get('action_description', 'N/A')}</p>
                <p style="margin: 5px 0;"><strong>Date:</strong> {vote_details.get('action_date', 'N/A')}</p>
                <p style="margin: 5px 0;"><strong>Organization:</strong> {vote_details.get('organization', 'N/A')}</p>
            </div>
            """
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Bill Status Update</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #007bff; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
                <h1 style="margin: 0; font-size: 24px;">Bill Status Update</h1>
            </div>
            
            <div style="background-color: #ffffff; padding: 30px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 8px 8px;">
                <p style="font-size: 16px; margin-bottom: 20px;">Hello,</p>
                
                <p style="font-size: 16px; margin-bottom: 20px;">
                    Great news! <strong>{bill_number}</strong>, a bill you took action on, has had a status update.
                </p>
                
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
                    <h3 style="margin: 0 0 15px 0; color: #007bff;">{bill_number}</h3>
                    <p style="margin: 0 0 10px 0; font-weight: bold;">{bill_title}</p>
                    <p style="margin: 0; color: #666;">
                        Status changed from <span style="color: #dc3545; font-weight: bold;">{previous_status.title()}</span> 
                        to <span style="color: #28a745; font-weight: bold;">{current_status.title()}</span>
                    </p>
                </div>
                
                {vote_info}
                
                <p style="font-size: 16px; margin: 20px 0;">
                    Your voice made a difference! Thank you for staying engaged in the democratic process.
                </p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="https://modernaction.org" 
                       style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                        Visit ModernAction
                    </a>
                </div>
                
                <p style="font-size: 14px; color: #666; margin-top: 30px; border-top: 1px solid #eee; padding-top: 20px;">
                    This email was sent because you took action on this bill through ModernAction. 
                    If you no longer wish to receive these updates, you can manage your preferences in your account settings.
                </p>
            </div>
        </body>
        </html>
        """
    
    def _generate_text_email_body(
        self, 
        bill_data: Dict[str, Any], 
        status_change: Dict[str, Any],
        user_action: Dict[str, Any]
    ) -> str:
        """Generate plain text email body"""
        bill_number = bill_data.get('bill_number', 'Unknown Bill')
        bill_title = bill_data.get('title', 'Unknown Title')
        previous_status = status_change['previous_status']
        current_status = status_change['current_status']
        vote_details = status_change.get('vote_details', {})
        
        # Format vote details if available
        vote_info = ""
        if vote_details:
            vote_info = f"""
Vote Details:
- Action: {vote_details.get('action_description', 'N/A')}
- Date: {vote_details.get('action_date', 'N/A')}
- Organization: {vote_details.get('organization', 'N/A')}
"""
        
        return f"""
BILL STATUS UPDATE

Hello,

Great news! {bill_number}, a bill you took action on, has had a status update.

{bill_number}
{bill_title}

Status changed from {previous_status.title()} to {current_status.title()}

{vote_info}

Your voice made a difference! Thank you for staying engaged in the democratic process.

Visit ModernAction: https://modernaction.org

---
This email was sent because you took action on this bill through ModernAction. 
If you no longer wish to receive these updates, you can manage your preferences in your account settings.
        """.strip()