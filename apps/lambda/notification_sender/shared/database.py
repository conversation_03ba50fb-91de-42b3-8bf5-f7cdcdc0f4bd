"""
Database connection module for Lambda functions.

This module provides database connectivity for serverless functions
with simplified session management for Lambda execution model.
"""

import os
import logging
from typing import Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import NullPool

logger = logging.getLogger(__name__)

# Global variables for Lambda function reuse
_engine = None
_session_factory = None


def get_database_engine():
    """
    Get or create database engine for Lambda function.
    
    Returns:
        SQLAlchemy engine instance
    """
    global _engine
    
    if _engine is None:
        # Get database connection parameters from environment
        db_host = os.getenv('DB_HOST')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'modernaction')
        db_user = os.getenv('DB_USER')
        db_password = os.getenv('DB_PASSWORD')
        
        if not all([db_host, db_user, db_password]):
            raise ValueError("Database connection parameters missing from environment")
        
        # Create connection string
        connection_string = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        
        # Create engine with Lambda-optimized settings
        _engine = create_engine(
            connection_string,
            poolclass=NullPool,  # No connection pooling in Lambda
            echo=False,  # Set to True for SQL debugging
            connect_args={
                "connect_timeout": 10,
                "options": "-c timezone=utc"
            }
        )
        
        logger.info("Database engine created")
    
    return _engine


def get_session_factory():
    """
    Get or create session factory for Lambda function.
    
    Returns:
        SQLAlchemy session factory
    """
    global _session_factory
    
    if _session_factory is None:
        engine = get_database_engine()
        _session_factory = sessionmaker(bind=engine)
        logger.info("Database session factory created")
    
    return _session_factory


def get_database_session() -> Optional[Session]:
    """
    Get a new database session for Lambda function.
    
    Returns:
        SQLAlchemy session instance or None if connection fails
    """
    try:
        session_factory = get_session_factory()
        session = session_factory()
        
        # Test the connection with a simple query
        session.execute(text("SELECT 1"))
        
        logger.info("Database session created successfully")
        return session
        
    except Exception as e:
        logger.error(f"Failed to create database session: {e}")
        return None


def test_database_connection() -> bool:
    """
    Test database connectivity.
    
    Returns:
        True if connection successful, False otherwise
    """
    try:
        session = get_database_session()
        if session:
            session.close()
            logger.info("Database connection test successful")
            return True
        else:
            logger.error("Database connection test failed")
            return False
            
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False