"""
Integration tests for the notification sender <PERSON><PERSON> function.

These tests use moto to mock AWS services (SQS and SES) and verify
the complete end-to-end functionality of email notifications.
"""

import json
import os
import pytest
from unittest.mock import Mock, patch, MagicMock
from moto import mock_sqs, mock_ses
import boto3

# Set environment variables for testing
os.environ.update({
    'AWS_REGION': 'us-east-1',
    'DB_HOST': 'localhost',
    'DB_PORT': '5432',
    'DB_NAME': 'test_db',
    'DB_USER': 'test_user',
    'DB_PASSWORD': 'test_password',
    'FROM_EMAIL': '<EMAIL>',
    'REPLY_TO_EMAIL': '<EMAIL>'
})

# Import the handler and services after setting environment variables
from handler import lambda_handler, process_bill_notification, get_bill_data, get_user_actions_for_bill
from shared.email_service import EmailService


class TestNotificationSenderLambda:
    """Test suite for the notification sender <PERSON><PERSON> function"""
    
    @mock_ses
    def test_lambda_handler_success(self):
        """Test successful Lambda execution with SES email sending"""
        # Set up SES
        ses_client = boto3.client('ses', region_name='us-east-1')
        ses_client.verify_email_identity(EmailAddress='<EMAIL>')
        
        # Mock database session
        mock_db = Mock()
        mock_db.commit.return_value = None
        mock_db.close.return_value = None
        
        # Create test SQS event
        event = {
            'Records': [
                {
                    'messageId': 'test-message-1',
                    'body': json.dumps({
                        'event_type': 'bill_status_change',
                        'bill_id': 1,
                        'previous_status': 'committee',
                        'current_status': 'floor',
                        'is_significant_change': True,
                        'vote_details': {
                            'action_description': 'Passed committee vote',
                            'action_date': '2024-07-18',
                            'organization': 'House Committee'
                        },
                        'timestamp': '2024-07-18T12:00:00Z'
                    })
                }
            ]
        }
        
        # Mock bill data and user actions
        mock_bill_data = {
            'id': 1,
            'title': 'Test Bill Title',
            'bill_number': 'HB 123',
            'status': 'floor'
        }
        
        mock_user_actions = [
            {
                'action_id': 1,
                'user_id': 1,
                'user_email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User',
                'message': 'I support this bill',
                'created_at': '2024-07-15T10:00:00Z'
            }
        ]
        
        with patch('shared.database.get_database_session', return_value=mock_db), \
             patch('handler.get_bill_data', return_value=mock_bill_data), \
             patch('handler.get_user_actions_for_bill', return_value=mock_user_actions), \
             patch('handler.mark_notification_sent') as mock_mark_sent:
            
            context = Mock()
            result = lambda_handler(event, context)
            
            # Verify response
            assert result['statusCode'] == 200
            assert result['body']['total_messages'] == 1
            assert result['body']['successful_notifications'] == 1
            assert result['body']['failed_notifications'] == 0
            
            # Verify database operations
            mock_db.commit.assert_called_once()
            mock_db.close.assert_called_once()
            mock_mark_sent.assert_called_once_with(mock_db, 1, 1)
    
    def test_lambda_handler_database_error(self):
        """Test Lambda handler with database connection failure"""
        with patch('shared.database.get_database_session', return_value=None):
            event = {'Records': []}
            context = Mock()
            
            result = lambda_handler(event, context)
            
            assert result['statusCode'] == 500
            assert 'Could not establish database connection' in result['body']['error']
    
    def test_lambda_handler_empty_event(self):
        """Test Lambda handler with empty SQS event"""
        mock_db = Mock()
        mock_db.commit.return_value = None
        mock_db.close.return_value = None
        
        with patch('shared.database.get_database_session', return_value=mock_db):
            event = {'Records': []}
            context = Mock()
            
            result = lambda_handler(event, context)
            
            assert result['statusCode'] == 200
            assert result['body']['total_messages'] == 0
            assert result['body']['successful_notifications'] == 0
    
    @mock_ses
    def test_lambda_handler_multiple_messages(self):
        """Test Lambda handler with multiple SQS messages"""
        # Set up SES
        ses_client = boto3.client('ses', region_name='us-east-1')
        ses_client.verify_email_identity(EmailAddress='<EMAIL>')
        
        mock_db = Mock()
        mock_db.commit.return_value = None
        mock_db.close.return_value = None
        
        # Create event with multiple messages
        event = {
            'Records': [
                {
                    'messageId': 'test-message-1',
                    'body': json.dumps({
                        'event_type': 'bill_status_change',
                        'bill_id': 1,
                        'previous_status': 'committee',
                        'current_status': 'floor',
                        'is_significant_change': True
                    })
                },
                {
                    'messageId': 'test-message-2',
                    'body': json.dumps({
                        'event_type': 'bill_status_change',
                        'bill_id': 2,
                        'previous_status': 'floor',
                        'current_status': 'passed',
                        'is_significant_change': True
                    })
                }
            ]
        }
        
        with patch('shared.database.get_database_session', return_value=mock_db), \
             patch('handler.process_bill_notification', return_value=True) as mock_process:
            
            context = Mock()
            result = lambda_handler(event, context)
            
            assert result['statusCode'] == 200
            assert result['body']['total_messages'] == 2
            assert mock_process.call_count == 2


class TestProcessBillNotification:
    """Test suite for the process_bill_notification function"""
    
    @mock_ses
    def test_process_bill_notification_success(self):
        """Test successful bill notification processing"""
        # Set up SES
        ses_client = boto3.client('ses', region_name='us-east-1')
        ses_client.verify_email_identity(EmailAddress='<EMAIL>')
        
        mock_db = Mock()
        mock_email_service = EmailService()
        
        message_data = {
            'bill_id': 1,
            'previous_status': 'committee',
            'current_status': 'floor',
            'is_significant_change': True
        }
        
        mock_bill_data = {
            'id': 1,
            'title': 'Test Bill',
            'bill_number': 'HB 123'
        }
        
        mock_user_actions = [
            {
                'user_id': 1,
                'user_email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        ]
        
        with patch('handler.get_bill_data', return_value=mock_bill_data), \
             patch('handler.get_user_actions_for_bill', return_value=mock_user_actions), \
             patch('handler.mark_notification_sent') as mock_mark_sent:
            
            result = process_bill_notification(mock_db, mock_email_service, message_data)
            
            assert result is True
            mock_mark_sent.assert_called_once_with(mock_db, 1, 1)
    
    def test_process_bill_notification_missing_bill_id(self):
        """Test processing with missing bill_id"""
        mock_db = Mock()
        mock_email_service = Mock()
        
        message_data = {'previous_status': 'committee'}
        
        result = process_bill_notification(mock_db, mock_email_service, message_data)
        
        assert result is False
    
    def test_process_bill_notification_bill_not_found(self):
        """Test processing when bill is not found"""
        mock_db = Mock()
        mock_email_service = Mock()
        
        message_data = {'bill_id': 999}
        
        with patch('handler.get_bill_data', return_value=None):
            result = process_bill_notification(mock_db, mock_email_service, message_data)
            
            assert result is False
    
    def test_process_bill_notification_no_user_actions(self):
        """Test processing when no user actions exist"""
        mock_db = Mock()
        mock_email_service = Mock()
        
        message_data = {'bill_id': 1}
        mock_bill_data = {'id': 1, 'title': 'Test Bill'}
        
        with patch('handler.get_bill_data', return_value=mock_bill_data), \
             patch('handler.get_user_actions_for_bill', return_value=[]):
            
            result = process_bill_notification(mock_db, mock_email_service, message_data)
            
            assert result is True  # Not an error, just no notifications to send


class TestEmailService:
    """Test suite for the EmailService"""
    
    @mock_ses
    def test_email_service_initialization(self):
        """Test email service initialization"""
        # Set up SES
        ses_client = boto3.client('ses', region_name='us-east-1')
        
        service = EmailService()
        assert service.from_email == '<EMAIL>'
        assert service.reply_to_email == '<EMAIL>'
    
    @mock_ses
    def test_send_vote_update_email_success(self):
        """Test successful email sending"""
        # Set up SES
        ses_client = boto3.client('ses', region_name='us-east-1')
        ses_client.verify_email_identity(EmailAddress='<EMAIL>')
        
        service = EmailService()
        
        bill_data = {
            'bill_number': 'HB 123',
            'title': 'Test Environmental Bill'
        }
        
        status_change = {
            'previous_status': 'committee',
            'current_status': 'floor',
            'vote_details': {
                'action_description': 'Passed committee vote',
                'action_date': '2024-07-18',
                'organization': 'House Environment Committee'
            }
        }
        
        user_action = {
            'user_id': 1,
            'message': 'I support this bill'
        }
        
        result = service.send_vote_update_email(
            '<EMAIL>',
            bill_data,
            status_change,
            user_action
        )
        
        assert result is True
    
    def test_generate_email_subject(self):
        """Test email subject generation"""
        service = EmailService()
        
        bill_data = {'bill_number': 'HB 123'}
        status_change = {'current_status': 'passed'}
        
        subject = service._generate_email_subject(bill_data, status_change)
        
        assert 'HB 123' in subject
        assert 'passed' in subject
    
    def test_generate_html_email_body(self):
        """Test HTML email body generation"""
        service = EmailService()
        
        bill_data = {
            'bill_number': 'HB 123',
            'title': 'Test Bill Title'
        }
        
        status_change = {
            'previous_status': 'committee',
            'current_status': 'floor',
            'vote_details': {
                'action_description': 'Passed committee vote',
                'action_date': '2024-07-18',
                'organization': 'House Committee'
            }
        }
        
        user_action = {'user_id': 1}
        
        html_body = service._generate_html_email_body(bill_data, status_change, user_action)
        
        assert 'HB 123' in html_body
        assert 'Test Bill Title' in html_body
        assert 'committee' in html_body.lower()
        assert 'floor' in html_body.lower()
        assert 'Passed committee vote' in html_body
        assert 'House Committee' in html_body
        assert 'modernaction.org' in html_body
    
    def test_generate_text_email_body(self):
        """Test plain text email body generation"""
        service = EmailService()
        
        bill_data = {
            'bill_number': 'HB 123',
            'title': 'Test Bill Title'
        }
        
        status_change = {
            'previous_status': 'committee',
            'current_status': 'floor',
            'vote_details': {
                'action_description': 'Passed committee vote'
            }
        }
        
        user_action = {'user_id': 1}
        
        text_body = service._generate_text_email_body(bill_data, status_change, user_action)
        
        assert 'HB 123' in text_body
        assert 'Test Bill Title' in text_body
        assert 'committee' in text_body.lower()
        assert 'floor' in text_body.lower()
        assert 'Passed committee vote' in text_body


class TestDatabaseFunctions:
    """Test suite for database helper functions"""
    
    def test_get_bill_data(self):
        """Test get_bill_data function"""
        mock_db = Mock()
        mock_result = Mock()
        mock_result._mapping = {
            'id': 1,
            'title': 'Test Bill',
            'bill_number': 'HB 123',
            'status': 'floor'
        }
        mock_db.execute.return_value.fetchone.return_value = mock_result
        
        result = get_bill_data(mock_db, 1)
        
        assert result['id'] == 1
        assert result['title'] == 'Test Bill'
        assert result['bill_number'] == 'HB 123'
    
    def test_get_bill_data_not_found(self):
        """Test get_bill_data when bill not found"""
        mock_db = Mock()
        mock_db.execute.return_value.fetchone.return_value = None
        
        result = get_bill_data(mock_db, 999)
        
        assert result is None
    
    def test_get_user_actions_for_bill(self):
        """Test get_user_actions_for_bill function"""
        mock_db = Mock()
        mock_rows = [
            Mock(_mapping={
                'action_id': 1,
                'user_id': 1,
                'user_email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }),
            Mock(_mapping={
                'action_id': 2,
                'user_id': 2,
                'user_email': '<EMAIL>',
                'first_name': 'Test2',
                'last_name': 'User2'
            })
        ]
        mock_db.execute.return_value = mock_rows
        
        result = get_user_actions_for_bill(mock_db, 1)
        
        assert len(result) == 2
        assert result[0]['user_email'] == '<EMAIL>'
        assert result[1]['user_email'] == '<EMAIL>'


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])