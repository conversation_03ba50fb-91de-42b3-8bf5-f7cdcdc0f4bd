# Bill Status Update Service - API Documentation

## Overview

The `BillStatusUpdateService` provides automated bill status monitoring and update functionality. It integrates with the OpenStates API to track legislative progress and maintains a comprehensive audit trail of all status changes.

## Service Class: `BillStatusUpdateService`

### Location
`apps/api/app/services/bill_status_update.py`

### Dependencies
- SQLAlchemy ORM for database operations
- Requests library for HTTP API calls
- OpenStates API for bill status data

### Initialization

```python
from app.services.bill_status_update import BillStatusUpdateService
from app.db.database import get_db

# Initialize with database session
db = get_db()
service = BillStatusUpdateService(db)
```

## Public Methods

### `get_active_bills() -> List[Bill]`

Retrieves all bills that require status monitoring.

**Returns:**
- `List[Bill]`: Bills in active status (draft, introduced, committee, floor)

**Example:**
```python
active_bills = service.get_active_bills()
print(f"Found {len(active_bills)} bills to monitor")
```

**Active Status Criteria:**
- Status is one of: `draft`, `introduced`, `committee`, `floor`
- Has a valid `openstates_id` for API lookup
- Ordered by `created_at` descending

---

### `fetch_bill_status_from_openstates(openstates_id: str) -> Optional[Dict[str, Any]]`

Fetches current bill data from the OpenStates API.

**Parameters:**
- `openstates_id` (str): OpenStates bill identifier

**Returns:**
- `Dict[str, Any]`: Raw bill data from API, or `None` if failed

**Example:**
```python
bill_data = service.fetch_bill_status_from_openstates("ocd-bill/12345")
if bill_data:
    print(f"Bill title: {bill_data['title']}")
```

**Error Handling:**
- Returns `None` on API failures
- Logs errors for monitoring
- Handles network timeouts gracefully

---

### `map_openstates_status_to_bill_status(openstates_data: Dict[str, Any]) -> BillStatus`

Converts OpenStates API data to our internal status enum.

**Parameters:**
- `openstates_data` (dict): Raw data from OpenStates API

**Returns:**
- `BillStatus`: Mapped status enum value

**Example:**
```python
api_data = {"actions": [{"description": "Passed House floor vote"}]}
status = service.map_openstates_status_to_bill_status(api_data)
print(f"Mapped status: {status}")  # BillStatus.FLOOR
```

**Status Mapping Logic:**
1. **Final Statuses** (checked first):
   - `signed` → `BillStatus.SIGNED`
   - `vetoed` → `BillStatus.VETOED`
   - `passed` + `final` → `BillStatus.PASSED`
   - `failed` or `defeated` → `BillStatus.FAILED`

2. **Intermediate Statuses**:
   - `floor` or `third reading` → `BillStatus.FLOOR`
   - `committee` → `BillStatus.COMMITTEE`

3. **Default**: `BillStatus.INTRODUCED`

---

### `is_significant_status_change(old_status: BillStatus, new_status: BillStatus) -> bool`

Determines if a status change warrants user notification.

**Parameters:**
- `old_status` (BillStatus): Previous bill status
- `new_status` (BillStatus): New bill status

**Returns:**
- `bool`: True if change is significant

**Example:**
```python
is_significant = service.is_significant_status_change(
    BillStatus.COMMITTEE, 
    BillStatus.FLOOR
)
print(f"Notify users: {is_significant}")  # True
```

**Significance Criteria:**
- Forward progression through legislative process
- Any transition to final status (passed, signed, vetoed, failed)
- Based on status hierarchy: draft < introduced < committee < floor < final

---

### `create_status_change_record(...) -> BillStatusPipeline`

Creates an audit record for a bill status change.

**Parameters:**
- `bill` (Bill): The bill that changed status
- `new_status` (BillStatus): The new status
- `external_data` (dict): Raw API data
- `status_changed_at` (datetime): When status actually changed

**Returns:**
- `BillStatusPipeline`: Created audit record

**Example:**
```python
from datetime import datetime

record = service.create_status_change_record(
    bill=bill_instance,
    new_status=BillStatus.PASSED,
    external_data=api_response,
    status_changed_at=datetime.utcnow()
)
print(f"Created record: {record.id}")
```

**Side Effects:**
- Updates the bill's current status
- Updates the bill's last_action_date
- Adds record to database session (requires commit)

---

### `update_bill_status(bill: Bill) -> Optional[BillStatusPipeline]`

Updates a single bill's status by checking the external API.

**Parameters:**
- `bill` (Bill): Bill instance to update

**Returns:**
- `BillStatusPipeline`: Status change record if updated, `None` if no change

**Example:**
```python
change_record = service.update_bill_status(bill)
if change_record:
    print(f"Status changed: {change_record.previous_status} → {change_record.current_status}")
else:
    print("No status change detected")
```

**Process:**
1. Validates bill has `openstates_id`
2. Fetches current status from API
3. Compares with database status
4. Creates change record if different
5. Updates bill in database

---

### `update_all_active_bills() -> Tuple[int, int]`

Updates status for all active bills in the database.

**Returns:**
- `Tuple[int, int]`: (total_checked, total_updated)

**Example:**
```python
checked, updated = service.update_all_active_bills()
print(f"Processed {checked} bills, updated {updated}")
```

**Process:**
1. Gets all active bills
2. Processes each bill sequentially
3. Handles individual failures gracefully
4. Commits successful updates
5. Rolls back failed updates

**Error Handling:**
- Individual bill failures don't stop processing
- Database rollback on errors
- Comprehensive logging for monitoring

## Database Models

### `BillStatusPipeline`

Audit trail model for tracking all bill status changes.

**Key Fields:**
```python
class BillStatusPipeline(Base):
    id: UUID                    # Primary key
    bill_id: UUID              # Foreign key to bills table
    previous_status: BillStatus # Status before change
    current_status: BillStatus  # Status after change
    status_changed_at: datetime # When change occurred
    detected_at: datetime      # When we detected it
    external_data: dict        # Raw API response
    vote_details: dict         # Extracted vote info
    notification_sent: bool    # User notification flag
    is_significant_change: bool # Notification worthiness
    notes: str                 # Additional context
```

**Relationships:**
```python
# From Bill model
bill.status_history  # List of BillStatusPipeline records

# From BillStatusPipeline model
pipeline_record.bill  # Associated Bill instance
```

## Usage Examples

### Basic Status Update

```python
from app.services.bill_status_update import BillStatusUpdateService
from app.db.database import get_db

# Initialize service
db = get_db()
service = BillStatusUpdateService(db)

# Update all active bills
try:
    total_checked, total_updated = service.update_all_active_bills()
    db.commit()
    print(f"Success: {total_updated}/{total_checked} bills updated")
except Exception as e:
    db.rollback()
    print(f"Error: {e}")
finally:
    db.close()
```

### Single Bill Update

```python
# Get specific bill
bill = db.query(Bill).filter(Bill.openstates_id == "ocd-bill/12345").first()

if bill:
    # Update just this bill
    change_record = service.update_bill_status(bill)
    
    if change_record:
        print(f"Bill {bill.bill_number} status changed!")
        print(f"Previous: {change_record.previous_status}")
        print(f"Current: {change_record.current_status}")
        print(f"Significant: {change_record.is_significant_change}")
        
        if change_record.vote_details:
            print(f"Vote info: {change_record.vote_details}")
    else:
        print("No status change detected")
```

### Query Status History

```python
# Get all status changes for a bill
bill = db.query(Bill).filter(Bill.id == bill_id).first()
status_history = bill.status_history

print(f"Status history for {bill.bill_number}:")
for record in status_history:
    print(f"  {record.detected_at}: {record.previous_status} → {record.current_status}")
    if record.is_significant_change:
        print(f"    ⚠️  Significant change - notification sent: {record.notification_sent}")
```

### Find Bills Needing Notifications

```python
# Query for significant changes that haven't been notified
pending_notifications = (
    db.query(BillStatusPipeline)
    .filter(BillStatusPipeline.is_significant_change == True)
    .filter(BillStatusPipeline.notification_sent == False)
    .all()
)

print(f"Found {len(pending_notifications)} bills needing notifications")
for record in pending_notifications:
    print(f"  Bill {record.bill.bill_number}: {record.current_status}")
```

## Error Handling

### Common Exceptions

```python
try:
    service.update_all_active_bills()
except requests.exceptions.RequestException as e:
    # API connection issues
    logger.error(f"OpenStates API error: {e}")
except sqlalchemy.exc.SQLAlchemyError as e:
    # Database issues
    logger.error(f"Database error: {e}")
    db.rollback()
except ValueError as e:
    # Configuration issues (missing API key, etc.)
    logger.error(f"Configuration error: {e}")
```

### Logging

The service uses Python's logging module:

```python
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Service logs these events:
# - INFO: Successful operations
# - WARNING: Recoverable issues
# - ERROR: Failures requiring attention
# - DEBUG: Detailed execution info
```

## Performance Considerations

### Database Optimization
- Uses indexed queries for active bill lookup
- Batch commits for multiple updates
- Connection reuse within service instance

### API Rate Limiting
- Sequential processing to respect OpenStates limits
- Graceful handling of rate limit responses
- Exponential backoff on failures

### Memory Management
- Processes bills one at a time
- Releases API response data after processing
- Minimal object retention

## Integration Points

### Lambda Function
The service is used by the Lambda function in `apps/lambda/bill_status_update/`:

```python
# Lambda handler usage
from shared.bill_status_service import BillStatusUpdateService

def lambda_handler(event, context):
    service = BillStatusUpdateService(db)
    total_checked, total_updated = service.update_all_active_bills()
    return {
        'statusCode': 200,
        'body': {
            'total_checked': total_checked,
            'total_updated': total_updated
        }
    }
```

### Background Tasks
Can be integrated with Celery or other task queues:

```python
from celery import Celery
from app.services.bill_status_update import BillStatusUpdateService

@celery.task
def update_bill_statuses():
    db = get_db()
    service = BillStatusUpdateService(db)
    return service.update_all_active_bills()
```

---

This API documentation provides comprehensive guidance for using the Bill Status Update Service in various contexts and integration scenarios.
