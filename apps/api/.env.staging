# Database Configuration
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/modernaction_staging
# Environment
ENVIRONMENT=development

# API Configuration
API_V1_STR=/api/v1
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# External API Keys
OPEN_STATES_API_KEY=6080864a-b9e2-4570-b67e-0abdfcdee059
GOOGLE_CIVIC_INFO_API_KEY=AIzaSyAA5ShGbL9sifQFSBdLKn2fGOa2JHvQ4To
HUGGING_FACE_API_KEY=*************************************

# AWS Configuration (for production)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key

# SES Configuration
AWS_SES_FROM_EMAIL=<EMAIL>

# Twitter Configuration (Optional - for Twitter integration)
TWITTER_API_KEY=*************************
TWITTER_API_SECRET=RKHOxJ0MljNuVGghLfVOjuc5hWWyPN0YgQ2c5kSzPH8xTzwiOf
TWITTER_ACCESS_TOKEN=1302311209195188227-kVVzytan1uNxuAFKQe9nP7eETtTEJl
TWITTER_ACCESS_TOKEN_SECRET=scVqhJ0ZSGYQB5Y09WeRbWyr3DlJBDzAJ6QzPxnYKog0p

# Redis (for caching and background tasks)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO