# Local Development Setup

This guide covers setting up the ModernAction API for local development with PostgreSQL.

## 🚀 Automated Setup (Recommended)

For a quick automated setup, run the setup script:

```bash
cd apps/api
./setup-dev.sh
```

This script will:
- ✅ Check prerequisites (Poetry, Docker)
- ✅ Install Python dependencies
- ✅ Create `.env` file with secure defaults
- ✅ Start PostgreSQL database
- ✅ Run database migrations
- ✅ Test the setup

## 📖 Manual Setup

If you prefer to set up manually or need to troubleshoot, follow the steps below.

## Prerequisites

- **Python 3.11+** with Poetry
- **Docker & Docker Compose** for PostgreSQL
- **Git** for version control

## Quick Start

### 1. Database Setup

Start the PostgreSQL database using Docker Compose:

```bash
# From the project root directory
docker-compose up -d db
```

This starts PostgreSQL with the following configuration:
- **Host**: localhost
- **Port**: 5432
- **Database**: modernaction
- **Username**: modernaction_user
- **Password**: modernaction_password

### 2. Environment Configuration

Copy the environment template and configure for local development:

```bash
cd apps/api
cp .env.example .env
```

Edit the `.env` file with your local settings. See the [Environment Variables](#environment-variables) section below for details.

### 3. Install Dependencies

```bash
poetry install
```

### 4. Database Migrations

Apply all database migrations:

```bash
poetry run alembic upgrade head
```

### 5. Start the API Server

```bash
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc

## Environment Variables

### Required Variables

Create a `.env` file in `apps/api/` with these required variables:

```bash
# Database Configuration
DATABASE_URL=postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction

# Security (generate secure random keys for production)
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings (adjust for your frontend)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

### Optional Variables

These are optional for local development but may be needed for full functionality:

```bash
# External APIs
OPENSTATES_API_KEY=your-openstates-api-key
CONGRESS_GOV_API_KEY=your-congress-gov-api-key

# Email Configuration (for testing email features)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# AWS SES (alternative to SMTP)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>

# Redis (for background tasks)
REDIS_URL=redis://localhost:6379

# Development Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
```

### Environment Variables Reference

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DATABASE_URL` | PostgreSQL connection string | - | ✅ |
| `SECRET_KEY` | JWT signing secret | - | ✅ |
| `ALGORITHM` | JWT algorithm | HS256 | ❌ |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | JWT expiration time | 30 | ❌ |
| `ALLOWED_ORIGINS` | CORS allowed origins | localhost:3000 | ❌ |
| `OPENSTATES_API_KEY` | OpenStates API key | - | ❌ |
| `CONGRESS_GOV_API_KEY` | Congress.gov API key | - | ❌ |
| `SMTP_HOST` | Email SMTP host | - | ❌ |
| `SMTP_PORT` | Email SMTP port | 587 | ❌ |
| `SMTP_USERNAME` | Email username | - | ❌ |
| `SMTP_PASSWORD` | Email password | - | ❌ |
| `FROM_EMAIL` | Default from email | - | ❌ |
| `AWS_ACCESS_KEY_ID` | AWS access key | - | ❌ |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key | - | ❌ |
| `AWS_REGION` | AWS region | us-east-1 | ❌ |
| `AWS_SES_FROM_EMAIL` | SES from email | - | ❌ |
| `REDIS_URL` | Redis connection string | - | ❌ |
| `ENVIRONMENT` | Environment name | development | ❌ |
| `DEBUG` | Enable debug mode | false | ❌ |
| `LOG_LEVEL` | Logging level | INFO | ❌ |

## Database Management

### Docker PostgreSQL

The PostgreSQL database runs in Docker with persistent storage:

```bash
# Start database
docker-compose up -d db

# Stop database
docker-compose stop db

# Remove database (destroys data)
docker-compose down -v

# View database logs
docker-compose logs db

# Connect to database directly
docker exec -it modern-action-20-db-1 psql -U modernaction_user -d modernaction
```

### Migrations

The project uses Alembic for database schema management:

```bash
# Check current migration version
poetry run alembic current

# View migration history
poetry run alembic history

# Apply all pending migrations
poetry run alembic upgrade head

# Rollback one migration
poetry run alembic downgrade -1

# Create a new migration (after model changes)
poetry run alembic revision --autogenerate -m "Description of changes"
```

### Database Schema

The current schema includes these tables:

- **users**: User accounts and profiles
- **bills**: Legislative bills and metadata
- **campaigns**: Advocacy campaigns
- **officials**: Elected officials and contact info
- **actions**: User actions (emails, calls, etc.)
- **bill_status_pipeline**: Bill status change tracking
- **alembic_version**: Migration version tracking

And these enum types:

- **actiontype**: EMAIL, PHONE, LETTER, SOCIAL_MEDIA, PETITION
- **actionstatus**: PENDING, SENT, DELIVERED, FAILED, BOUNCED
- **billstatus**: DRAFT, INTRODUCED, COMMITTEE, FLOOR, PASSED, SIGNED, VETOED, FAILED

## Development Workflow

### Code Changes

1. Make your changes to the code
2. The development server will auto-reload (thanks to `--reload` flag)
3. Test your changes via the API docs at http://localhost:8000/docs

### Database Changes

1. Modify SQLAlchemy models in `app/models/`
2. Generate a migration: `poetry run alembic revision --autogenerate -m "Description"`
3. Review the generated migration file in `alembic/versions/`
4. Apply the migration: `poetry run alembic upgrade head`

### Testing

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=app

# Run specific test file
poetry run pytest tests/test_bills.py

# Run tests in verbose mode
poetry run pytest -v
```

## Troubleshooting

### Common Issues

#### Database Connection Errors

```
sqlalchemy.exc.OperationalError: could not connect to server
```

**Solutions**:
1. Ensure PostgreSQL is running: `docker-compose ps`
2. Check the DATABASE_URL in your `.env` file
3. Restart the database: `docker-compose restart db`

#### Migration Errors

```
alembic.util.exc.CommandError: Target database is not up to date
```

**Solutions**:
1. Check current version: `poetry run alembic current`
2. Apply pending migrations: `poetry run alembic upgrade head`
3. If corrupted, reset database: `docker-compose down -v && docker-compose up -d db`

#### Port Already in Use

```
OSError: [Errno 48] Address already in use
```

**Solutions**:
1. Use a different port: `uvicorn app.main:app --port 8001`
2. Kill existing process: `lsof -ti:8000 | xargs kill`

#### Import Errors

```
ModuleNotFoundError: No module named 'app'
```

**Solutions**:
1. Ensure you're in the correct directory: `cd apps/api`
2. Activate poetry environment: `poetry shell`
3. Install dependencies: `poetry install`

### Debug Mode

Enable detailed logging by setting these in your `.env`:

```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

### Getting Help

1. Check the main [README.md](README.md) for architecture details
2. View API documentation at http://localhost:8000/docs
3. Check the [troubleshooting section](#troubleshooting) above
4. Create an issue in the repository

## Next Steps

Once you have the local environment running:

1. **Explore the API**: Visit http://localhost:8000/docs to see all available endpoints
2. **Add test data**: Use the API to create some bills, campaigns, and actions
3. **Frontend integration**: Connect your frontend application to http://localhost:8000
4. **External APIs**: Add API keys for OpenStates and Congress.gov for real bill data
5. **Email testing**: Configure SMTP or AWS SES to test email functionality

## 🚀 Automated Setup (Recommended)

For a quick automated setup, run the setup script:

```bash
cd apps/api
./setup-dev.sh
```

This script will:
- ✅ Check prerequisites (Poetry, Docker)
- ✅ Install Python dependencies
- ✅ Create `.env` file with secure defaults
- ✅ Start PostgreSQL database
- ✅ Run database migrations
- ✅ Test the setup

## 📖 Manual Setup

If you prefer to set up manually or need to troubleshoot, follow the steps below.

## Prerequisites

- **Python 3.11+** with Poetry
- **Docker & Docker Compose** for PostgreSQL
- **Git** for version control

## Quick Start

### 1. Database Setup

Start the PostgreSQL database using Docker Compose:

```bash
# From the project root directory
docker-compose up -d db
```

This starts PostgreSQL with the following configuration:
- **Host**: localhost
- **Port**: 5432
- **Database**: modernaction
- **Username**: modernaction_user
- **Password**: modernaction_password

### 2. Environment Configuration

Copy the environment template and configure for local development:

```bash
cd apps/api
cp .env.example .env
```

Edit the `.env` file with your local settings. See the [Environment Variables](#environment-variables) section below for details.

### 3. Install Dependencies

```bash
poetry install
```

### 4. Database Migrations

Apply all database migrations:

```bash
poetry run alembic upgrade head
```

### 5. Start the API Server

```bash
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc

## Environment Variables

### Required Variables

Create a `.env` file in `apps/api/` with these required variables:

```bash
# Database Configuration
DATABASE_URL=postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction

# Security (generate secure random keys for production)
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings (adjust for your frontend)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```
