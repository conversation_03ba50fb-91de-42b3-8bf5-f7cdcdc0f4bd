"""Add action model and relationships

Revision ID: 002
Revises: 001
Create Date: 2024-07-17 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # Create enums for action types and status
    action_status = postgresql.ENUM('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', name='actionstatus')
    action_status.create(op.get_bind(), checkfirst=True)

    action_type = postgresql.ENUM('EMAIL', 'PHONE', 'LETTER', 'SOCIAL_MEDIA', 'PETITION', name='actiontype')
    action_type.create(op.get_bind(), checkfirst=True)

    # We won't try to convert the columns to enums yet - that will be done in a separate migration
    # This migration just creates the enum types for future use

    # For now, just create the enums. Column additions will be done in a separate migration
    # to avoid complexity and transaction issues
    
    # Skip index creation for now - will be added in a separate migration if needed
    pass


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_actions_official_id'), table_name='actions')
    op.drop_index(op.f('ix_actions_user_id'), table_name='actions')
    op.drop_index(op.f('ix_actions_campaign_id'), table_name='actions')
    op.drop_index(op.f('ix_actions_status'), table_name='actions')
    op.drop_index(op.f('ix_actions_created_at'), table_name='actions')
    
    # Drop actions table
    op.drop_table('actions')
    
    # Drop enums
    op.execute('DROP TYPE actiontype')
    op.execute('DROP TYPE actionstatus')