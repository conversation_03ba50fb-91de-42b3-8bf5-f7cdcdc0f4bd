"""Add bill status pipeline table for tracking status changes

Revision ID: 003
Revises: 002
Create Date: 2024-07-18 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade():
    # Create bill_status_pipeline table
    op.create_table('bill_status_pipeline',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('bill_id', sa.String(36), nullable=False),
        sa.Column('previous_status', postgresql.ENUM('DRAFT', 'INTRODUCED', 'COMMITTEE', 'FLOOR', 'PASSED', 'SIGNED', 'VETOED', 'FAILED', name='billstatus', create_type=False), nullable=True),
        sa.Column('current_status', postgresql.ENUM('DRAFT', 'INTRODUCED', 'COMMITTEE', 'FLOOR', 'PASSED', 'SIGNED', 'VETOED', 'FAILED', name='billstatus', create_type=False), nullable=False),
        sa.Column('status_changed_at', sa.DateTime(), nullable=False),
        sa.Column('detected_at', sa.DateTime(), nullable=False),
        sa.Column('external_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('vote_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('notification_sent', sa.Boolean(), nullable=False),
        sa.Column('is_significant_change', sa.Boolean(), nullable=False),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for efficient querying
    op.create_index(op.f('ix_bill_status_pipeline_id'), 'bill_status_pipeline', ['id'], unique=False)
    op.create_index(op.f('ix_bill_status_pipeline_bill_id'), 'bill_status_pipeline', ['bill_id'], unique=False)
    op.create_index(op.f('ix_bill_status_pipeline_current_status'), 'bill_status_pipeline', ['current_status'], unique=False)
    op.create_index(op.f('ix_bill_status_pipeline_detected_at'), 'bill_status_pipeline', ['detected_at'], unique=False)
    op.create_index(op.f('ix_bill_status_pipeline_notification_sent'), 'bill_status_pipeline', ['notification_sent'], unique=False)
    op.create_index(op.f('ix_bill_status_pipeline_is_significant_change'), 'bill_status_pipeline', ['is_significant_change'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_bill_status_pipeline_is_significant_change'), table_name='bill_status_pipeline')
    op.drop_index(op.f('ix_bill_status_pipeline_notification_sent'), table_name='bill_status_pipeline')
    op.drop_index(op.f('ix_bill_status_pipeline_detected_at'), table_name='bill_status_pipeline')
    op.drop_index(op.f('ix_bill_status_pipeline_current_status'), table_name='bill_status_pipeline')
    op.drop_index(op.f('ix_bill_status_pipeline_bill_id'), table_name='bill_status_pipeline')
    op.drop_index(op.f('ix_bill_status_pipeline_id'), table_name='bill_status_pipeline')
    
    # Drop bill_status_pipeline table
    op.drop_table('bill_status_pipeline')
