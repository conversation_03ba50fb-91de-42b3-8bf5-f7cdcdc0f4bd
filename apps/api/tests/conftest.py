import pytest
import os
import uuid
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.db.database import get_db
from app.db.base_class import Base
from app.core.config import settings


# Test database configuration
TEST_DB_HOST = os.getenv("TEST_DB_HOST", "localhost")
TEST_DB_PORT = os.getenv("TEST_DB_PORT", "5432")
TEST_DB_USER = os.getenv("TEST_DB_USER", "modernaction_user")
TEST_DB_PASSWORD = os.getenv("TEST_DB_PASSWORD", "modernaction_password")
TEST_DB_NAME_PREFIX = "modernaction_test"


@pytest.fixture(scope="session")
def test_database_name():
    """Generate a unique test database name for this test session"""
    return f"{TEST_DB_NAME_PREFIX}_{uuid.uuid4().hex[:8]}"


@pytest.fixture(scope="session")
def test_engine(test_database_name):
    """Create a test database and engine for the entire test session"""
    # Connect to PostgreSQL server (not to a specific database)
    server_url = f"postgresql://{TEST_DB_USER}:{TEST_DB_PASSWORD}@{TEST_DB_HOST}:{TEST_DB_PORT}/postgres"
    server_engine = create_engine(server_url, isolation_level="AUTOCOMMIT")

    # Create test database
    with server_engine.connect() as conn:
        conn.execute(text(f'CREATE DATABASE "{test_database_name}"'))

    # Create engine for test database
    test_url = f"postgresql://{TEST_DB_USER}:{TEST_DB_PASSWORD}@{TEST_DB_HOST}:{TEST_DB_PORT}/{test_database_name}"
    test_engine = create_engine(test_url)

    # Create all tables
    Base.metadata.create_all(bind=test_engine)

    yield test_engine

    # Cleanup: drop test database
    test_engine.dispose()
    with server_engine.connect() as conn:
        # Terminate any remaining connections to the test database
        conn.execute(text(f"""
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = '{test_database_name}' AND pid <> pg_backend_pid()
        """))
        conn.execute(text(f'DROP DATABASE IF EXISTS "{test_database_name}"'))
    server_engine.dispose()


@pytest.fixture
def test_db_session(test_engine):
    """
    Yields a new, isolated transaction for each test function.
    Rolls back the transaction after the test is complete.
    This ensures perfect test isolation.
    """
    connection = test_engine.connect()

    # begin a non-ORM transaction
    transaction = connection.begin()

    # bind an individual session to the connection
    Session = sessionmaker(bind=connection)
    session = Session()

    yield session

    # rollback the transaction and close the connection
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def test_db(test_engine):
    """Create a test database session factory for each test function"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    yield TestingSessionLocal


@pytest.fixture(scope="function")
def test_client(test_db_session):
    """Create a test client with test database"""
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass  # Session cleanup handled by test_db_session fixture

    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as client:
        yield client

    app.dependency_overrides.clear()


@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User",
        "zip_code": "12345"
    }


@pytest.fixture
def sample_bill_data():
    """Sample bill data for testing"""
    return {
        "title": "Test Bill",
        "description": "A test bill for testing purposes",
        "bill_number": "HR-123",
        "bill_type": "house_bill",
        "status": "introduced",
        "session_year": 2024,
        "chamber": "house",
        "state": "federal",
        "full_text": "This is the full text of the test bill.",
        "summary": "This is a summary of the test bill.",
        "is_featured": False,
        "priority_score": 0
    }


@pytest.fixture
def sample_official_data():
    """Sample official data for testing"""
    return {
        "name": "Test Official",
        "title": "Representative",
        "party": "Independent",
        "email": "<EMAIL>",
        "phone": "555-0123",
        "level": "federal",
        "chamber": "house",
        "state": "CA",
        "district": "1",
        "is_active": True
    }


@pytest.fixture
def sample_campaign_data():
    """Sample campaign data for testing"""
    return {
        "title": "Test Campaign",
        "description": "A test campaign for testing purposes",
        "campaign_type": "support",
        "status": "active",
        "call_to_action": "Support this important legislation!",
        "is_featured": False,
        "is_public": True,
        "requires_verification": False,
        "actual_actions": 0
    }