# tests/test_twitter_service.py
"""
Test suite for Twitter service functionality.

These tests verify the Twitter service including tweet posting,
error handling, and integration with the action workflow.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from app.services.twitter import TwitterService, post_tweet, post_action_tweet, health_check


class TestTwitterService:
    """Test class for Twitter service functionality"""

    def test_twitter_service_initialization_success(self):
        """Test successful Twitter service initialization"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings with valid credentials
            mock_settings.TWITTER_API_KEY = "test_api_key"
            mock_settings.TWITTER_API_SECRET = "test_api_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_access_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_access_token_secret"
            
            # Mock tweepy client
            mock_client = Mock()
            mock_user = Mock()
            mock_user.data.username = "testuser"
            mock_client.get_me.return_value = mock_user
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service
            service = TwitterService()
            
            assert service.client is not None
            mock_tweepy.Client.assert_called_once_with(
                consumer_key="test_api_key",
                consumer_secret="test_api_secret",
                access_token="test_access_token",
                access_token_secret="test_access_token_secret",
                wait_on_rate_limit=True
            )

    def test_twitter_service_initialization_missing_credentials(self):
        """Test Twitter service initialization with missing credentials"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings with missing credentials
            mock_settings.TWITTER_API_KEY = None
            mock_settings.TWITTER_API_SECRET = "test_api_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_access_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_access_token_secret"
            
            # Initialize service
            service = TwitterService()
            
            assert service.client is None

    def test_post_tweet_success(self):
        """Test successful tweet posting"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings
            mock_settings.TWITTER_API_KEY = "test_key"
            mock_settings.TWITTER_API_SECRET = "test_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_token_secret"
            
            # Mock successful tweet response
            mock_client = Mock()
            mock_response = Mock()
            mock_response.data = {'id': '1234567890'}
            mock_client.create_tweet.return_value = mock_response
            mock_client.get_me.return_value = Mock(data=Mock(username="testuser"))
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service and post tweet
            service = TwitterService()
            result = service.post_tweet("Test message")
            
            assert result["success"] is True
            assert result["tweet_id"] == "1234567890"
            assert result["tweet_url"] == "https://twitter.com/user/status/1234567890"
            assert result["message"] == "Test message"
            assert result["character_count"] == 12
            
            mock_client.create_tweet.assert_called_once_with(text="Test message")

    def test_post_tweet_with_mention(self):
        """Test tweet posting with mention"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings and client
            mock_settings.TWITTER_API_KEY = "test_key"
            mock_settings.TWITTER_API_SECRET = "test_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_token_secret"
            
            mock_client = Mock()
            mock_response = Mock()
            mock_response.data = {'id': '1234567890'}
            mock_client.create_tweet.return_value = mock_response
            mock_client.get_me.return_value = Mock(data=Mock(username="testuser"))
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service and post tweet with mention
            service = TwitterService()
            result = service.post_tweet("Test message", reply_to_username="senator_smith")
            
            assert result["success"] is True
            mock_client.create_tweet.assert_called_once_with(text="@senator_smith Test message")

    def test_post_tweet_rate_limit_error(self):
        """Test tweet posting with rate limit error"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings
            mock_settings.TWITTER_API_KEY = "test_key"
            mock_settings.TWITTER_API_SECRET = "test_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_token_secret"
            
            # Mock rate limit error
            mock_client = Mock()

            # Create proper exception classes that inherit from Exception
            class MockTooManyRequests(Exception):
                pass

            class MockUnauthorized(Exception):
                pass

            class MockForbidden(Exception):
                pass

            mock_tweepy.TooManyRequests = MockTooManyRequests
            mock_tweepy.Unauthorized = MockUnauthorized
            mock_tweepy.Forbidden = MockForbidden
            mock_client.create_tweet.side_effect = MockTooManyRequests("Rate limit exceeded")
            mock_client.get_me.return_value = Mock(data=Mock(username="testuser"))
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service and post tweet
            service = TwitterService()
            result = service.post_tweet("Test message")

            assert result["success"] is False
            assert result["error_code"] == "RATE_LIMIT_EXCEEDED"
            assert "rate limit exceeded" in result["error_message"].lower()

    def test_post_tweet_unauthorized_error(self):
        """Test tweet posting with unauthorized error"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings
            mock_settings.TWITTER_API_KEY = "test_key"
            mock_settings.TWITTER_API_SECRET = "test_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_token_secret"
            
            # Mock unauthorized error
            mock_client = Mock()

            # Create proper exception classes that inherit from Exception
            class MockTooManyRequests(Exception):
                pass

            class MockUnauthorized(Exception):
                pass

            class MockForbidden(Exception):
                pass

            mock_tweepy.TooManyRequests = MockTooManyRequests
            mock_tweepy.Unauthorized = MockUnauthorized
            mock_tweepy.Forbidden = MockForbidden
            mock_client.create_tweet.side_effect = MockUnauthorized("Invalid credentials")
            mock_client.get_me.return_value = Mock(data=Mock(username="testuser"))
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service and post tweet
            service = TwitterService()
            result = service.post_tweet("Test message")

            assert result["success"] is False
            assert result["error_code"] == "UNAUTHORIZED"

    def test_post_tweet_client_not_initialized(self):
        """Test tweet posting when client is not initialized"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings with missing credentials
            mock_settings.TWITTER_API_KEY = None
            mock_settings.TWITTER_API_SECRET = None
            mock_settings.TWITTER_ACCESS_TOKEN = None
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = None
            
            # Initialize service (should have no client)
            service = TwitterService()
            
            # Attempt to post tweet
            with pytest.raises(RuntimeError, match="Twitter client is not initialized"):
                service.post_tweet("Test message")

    def test_post_tweet_empty_message(self):
        """Test tweet posting with empty message"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings
            mock_settings.TWITTER_API_KEY = "test_key"
            mock_settings.TWITTER_API_SECRET = "test_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_token_secret"
            
            mock_client = Mock()
            mock_client.get_me.return_value = Mock(data=Mock(username="testuser"))
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service
            service = TwitterService()
            
            # Attempt to post empty tweet
            with pytest.raises(ValueError, match="Tweet message cannot be empty"):
                service.post_tweet("")

    def test_post_tweet_long_message_truncation(self):
        """Test tweet posting with message longer than 280 characters"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings
            mock_settings.TWITTER_API_KEY = "test_key"
            mock_settings.TWITTER_API_SECRET = "test_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_token_secret"
            
            mock_client = Mock()
            mock_response = Mock()
            mock_response.data = {'id': '1234567890'}
            mock_client.create_tweet.return_value = mock_response
            mock_client.get_me.return_value = Mock(data=Mock(username="testuser"))
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service
            service = TwitterService()
            
            # Create a message longer than 280 characters
            long_message = "A" * 300
            result = service.post_tweet(long_message)
            
            assert result["success"] is True
            assert result["character_count"] == 280  # Should be truncated
            
            # Check that the truncated message was posted
            call_args = mock_client.create_tweet.call_args[1]
            posted_message = call_args['text']
            assert len(posted_message) == 280
            assert posted_message.endswith("...")

    def test_post_action_tweet_success(self):
        """Test successful action tweet posting"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings
            mock_settings.TWITTER_API_KEY = "test_key"
            mock_settings.TWITTER_API_SECRET = "test_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_token_secret"
            
            mock_client = Mock()
            mock_response = Mock()
            mock_response.data = {'id': '1234567890'}
            mock_client.create_tweet.return_value = mock_response
            mock_client.get_me.return_value = Mock(data=Mock(username="testuser"))
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service
            service = TwitterService()
            
            # Post action tweet
            result = service.post_action_tweet(
                message="Please support climate action legislation",
                official_twitter_handle="senator_smith",
                campaign_hashtag="ClimateAction",
                user_name="John Doe"
            )
            
            assert result["success"] is True
            
            # Check that the tweet was formatted correctly
            call_args = mock_client.create_tweet.call_args[1]
            posted_message = call_args['text']
            assert "@senator_smith" in posted_message
            assert "Please support climate action legislation" in posted_message
            assert "#ClimateAction" in posted_message
            assert "John Doe" in posted_message

    def test_health_check_healthy(self):
        """Test health check when service is healthy"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings
            mock_settings.TWITTER_API_KEY = "test_key"
            mock_settings.TWITTER_API_SECRET = "test_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_token_secret"
            
            mock_client = Mock()
            mock_user = Mock()
            mock_user.data.username = "testuser"
            mock_client.get_me.return_value = mock_user
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service
            service = TwitterService()
            result = service.health_check()
            
            assert result["healthy"] is True
            assert result["service"] == "twitter"
            assert result["authenticated_user"] == "testuser"

    def test_health_check_unhealthy(self):
        """Test health check when service is unhealthy"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings with missing credentials
            mock_settings.TWITTER_API_KEY = None
            mock_settings.TWITTER_API_SECRET = None
            mock_settings.TWITTER_ACCESS_TOKEN = None
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = None
            
            # Initialize service
            service = TwitterService()
            result = service.health_check()
            
            assert result["healthy"] is False
            assert result["service"] == "twitter"
            assert "not initialized" in result["error"]

    def test_is_available_true(self):
        """Test is_available returns True when client is initialized"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings
            mock_settings.TWITTER_API_KEY = "test_key"
            mock_settings.TWITTER_API_SECRET = "test_secret"
            mock_settings.TWITTER_ACCESS_TOKEN = "test_token"
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = "test_token_secret"
            
            mock_client = Mock()
            mock_client.get_me.return_value = Mock(data=Mock(username="testuser"))
            mock_tweepy.Client.return_value = mock_client
            
            # Initialize service
            service = TwitterService()
            
            assert service.is_available() is True

    def test_is_available_false(self):
        """Test is_available returns False when client is not initialized"""
        with patch('app.services.twitter.tweepy') as mock_tweepy, \
             patch('app.services.twitter.settings') as mock_settings:
            
            # Mock settings with missing credentials
            mock_settings.TWITTER_API_KEY = None
            mock_settings.TWITTER_API_SECRET = None
            mock_settings.TWITTER_ACCESS_TOKEN = None
            mock_settings.TWITTER_ACCESS_TOKEN_SECRET = None
            
            # Initialize service
            service = TwitterService()
            
            assert service.is_available() is False


class TestTwitterConvenienceFunctions:
    """Test convenience functions for Twitter service"""

    def test_post_tweet_convenience_function(self):
        """Test the post_tweet convenience function"""
        with patch('app.services.twitter.TwitterService') as mock_service_class:
            mock_service = Mock()
            mock_service.post_tweet.return_value = {"success": True, "tweet_id": "123"}
            mock_service_class.return_value = mock_service
            
            result = post_tweet("Test message")
            
            assert result["success"] is True
            mock_service.post_tweet.assert_called_once_with("Test message")

    def test_post_action_tweet_convenience_function(self):
        """Test the post_action_tweet convenience function"""
        with patch('app.services.twitter.TwitterService') as mock_service_class:
            mock_service = Mock()
            mock_service.post_action_tweet.return_value = {"success": True, "tweet_id": "123"}
            mock_service_class.return_value = mock_service
            
            result = post_action_tweet("Test message", "senator_smith")
            
            assert result["success"] is True
            mock_service.post_action_tweet.assert_called_once_with(
                message="Test message",
                official_twitter_handle="senator_smith"
            )

    def test_health_check_convenience_function(self):
        """Test the health_check convenience function"""
        with patch('app.services.twitter.TwitterService') as mock_service_class:
            mock_service = Mock()
            mock_service.health_check.return_value = {"healthy": True}
            mock_service_class.return_value = mock_service
            
            result = health_check()
            
            assert result["healthy"] is True
            mock_service.health_check.assert_called_once()
