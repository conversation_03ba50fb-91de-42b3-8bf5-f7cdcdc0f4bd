# =============================================================================
# ModernAction API Environment Configuration
# =============================================================================
# Copy this file to .env and update the values for your local development setup
#
# REQUIRED VARIABLES (must be set for the API to work):
# - DATABASE_URL
# - SECRET_KEY
#
# OPTIONAL VARIABLES (can be left as defaults for basic functionality):
# - All others
# =============================================================================

# -----------------------------------------------------------------------------
# Database Configuration
# -----------------------------------------------------------------------------
# For local development with Docker PostgreSQL (recommended)
DATABASE_URL=postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction

# For local development with your own PostgreSQL instance
# DATABASE_URL=postgresql://username:password@localhost:5432/modernaction

# For production (use environment-specific values)
# DATABASE_URL=********************************************/modernaction

# -----------------------------------------------------------------------------
# Environment Settings
# -----------------------------------------------------------------------------
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# -----------------------------------------------------------------------------
# Security Configuration
# -----------------------------------------------------------------------------
# IMPORTANT: Generate secure random keys for production!
# You can generate a secure key with: openssl rand -hex 32
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Legacy JWT secret (if needed for compatibility)
JWT_SECRET=your-jwt-secret-here

# -----------------------------------------------------------------------------
# API Configuration
# -----------------------------------------------------------------------------
API_V1_STR=/api/v1
PROJECT_NAME=ModernAction API

# CORS Settings - adjust for your frontend URLs
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
# For production, use your actual domain:
# ALLOWED_ORIGINS=https://modernaction.org,https://www.modernaction.org

# Alternative CORS variable name (for compatibility)
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# -----------------------------------------------------------------------------
# External API Keys (Optional - for enhanced functionality)
# -----------------------------------------------------------------------------
# OpenStates API for legislative data
OPENSTATES_API_KEY=your-openstates-api-key

# Congress.gov API for federal bill data
CONGRESS_GOV_API_KEY=your-congress-gov-api-key

# Google Civic Information API for representative lookup
GOOGLE_CIVIC_INFO_API_KEY=your-google-civic-info-api-key

# Hugging Face API for AI/ML features
HUGGING_FACE_API_KEY=your-hugging-face-api-key

# -----------------------------------------------------------------------------
# Email Configuration (Choose one method)
# -----------------------------------------------------------------------------

# Method 1: SMTP Configuration (for development/testing)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=ModernAction

# Method 2: AWS SES Configuration (for production)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=ModernAction
SES_REGION=us-east-1

# -----------------------------------------------------------------------------
# Redis Configuration (Optional - for caching and background tasks)
# -----------------------------------------------------------------------------
REDIS_URL=redis://localhost:6379/0

# For production Redis with authentication:
# REDIS_URL=redis://username:password@redis-host:6379/0

# -----------------------------------------------------------------------------
# Additional Development Settings
# -----------------------------------------------------------------------------
# Enable SQL query logging (useful for debugging)
# SQL_ECHO=true

# API rate limiting (requests per minute)
# RATE_LIMIT_PER_MINUTE=60

# Maximum file upload size (in MB)
# MAX_UPLOAD_SIZE_MB=10

# -----------------------------------------------------------------------------
# Production-Only Settings (ignore for local development)
# -----------------------------------------------------------------------------
# Sentry DSN for error tracking
# SENTRY_DSN=https://<EMAIL>/project-id

# Application monitoring
# NEW_RELIC_LICENSE_KEY=your-new-relic-key

# SSL/TLS settings
# SSL_REDIRECT=true
# SECURE_COOKIES=true

# Database connection pool settings
# DB_POOL_SIZE=20
# DB_MAX_OVERFLOW=30
# DB_POOL_TIMEOUT=30

# -----------------------------------------------------------------------------
# Feature Flags (Optional)
# -----------------------------------------------------------------------------
# Enable/disable specific features
# ENABLE_AI_SUMMARIZATION=true
# ENABLE_EMAIL_SENDING=true
# ENABLE_BACKGROUND_TASKS=true
# ENABLE_RATE_LIMITING=true