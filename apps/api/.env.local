# Local Development Database Configuration
# Use this for local development and migrations
DATABASE_URL=postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction

# Environment
ENVIRONMENT=development

# API Configuration
API_V1_STR=/api/v1
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Security
SECRET_KEY=development-secret-key-change-in-production
JWT_SECRET=development-jwt-secret-change-in-production

# External API Keys (same as production)
OPEN_STATES_API_KEY=6080864a-b9e2-4570-b67e-0abdfcdee059
GOOGLE_CIVIC_INFO_API_KEY=AIzaSyAA5ShGbL9sifQFSBdLKn2fGOa2JHvQ4To
HUGGING_FACE_API_KEY=*************************************

# AWS Configuration (for local testing)
AWS_REGION=us-east-1

# SES Configuration
AWS_SES_FROM_EMAIL=<EMAIL>

# Twitter Configuration
TWITTER_API_KEY=*************************
TWITTER_API_SECRET=RKHOxJ0MljNuVGghLfVOjuc5hWWyPN0YgQ2c5kSzPH8xTzwiOf
TWITTER_ACCESS_TOKEN=1302311209195188227-kVVzytan1uNxuAFKQe9nP7eETtTEJl
TWITTER_ACCESS_TOKEN_SECRET=scVqhJ0ZSGYQB5Y09WeRbWyr3DlJBDzAJ6QzPxnYKog0p

# Redis (for caching and background tasks)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
