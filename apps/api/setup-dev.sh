#!/bin/bash

# ModernAction API Development Setup Script
# This script helps set up the local development environment

set -e  # Exit on any error

echo "🚀 ModernAction API Development Setup"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    print_error "Please run this script from the apps/api directory"
    exit 1
fi

print_status "Checking prerequisites..."

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    print_error "Poetry is not installed. Please install Poetry first:"
    echo "  curl -sSL https://install.python-poetry.org | python3 -"
    exit 1
fi

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_success "All prerequisites are available"

# Step 1: Install Python dependencies
print_status "Installing Python dependencies with Poetry..."
poetry install

# Step 2: Set up environment file
print_status "Setting up environment configuration..."
if [ ! -f ".env" ]; then
    print_status "Creating .env file from template..."
    cp .env.example .env
    
    # Generate a random secret key
    SECRET_KEY=$(openssl rand -hex 32)
    
    # Update the .env file with the generated secret key
    if command -v sed &> /dev/null; then
        sed -i.bak "s/your-secret-key-here-change-in-production/$SECRET_KEY/" .env
        rm .env.bak 2>/dev/null || true
    else
        print_warning "Could not automatically set SECRET_KEY. Please update it manually in .env"
    fi
    
    print_success "Created .env file with generated SECRET_KEY"
else
    print_warning ".env file already exists, skipping creation"
fi

# Step 3: Start PostgreSQL database
print_status "Starting PostgreSQL database with Docker Compose..."
cd ../..  # Go to project root for docker-compose
if docker-compose ps | grep -q "modern-action-20-db-1.*Up"; then
    print_warning "PostgreSQL database is already running"
else
    docker-compose up -d db
    print_success "PostgreSQL database started"
fi

# Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 5

# Go back to API directory
cd apps/api

# Step 4: Run database migrations
print_status "Running database migrations..."
poetry run alembic upgrade head
print_success "Database migrations completed"

# Step 5: Test the setup
print_status "Testing the setup..."

# Test database connection
if poetry run python -c "
from app.database import engine
from sqlalchemy import text
try:
    with engine.connect() as conn:
        conn.execute(text('SELECT 1'))
    print('✓ Database connection successful')
except Exception as e:
    print(f'✗ Database connection failed: {e}')
    exit(1)
" 2>/dev/null; then
    print_success "Database connection test passed"
else
    print_error "Database connection test failed"
    exit 1
fi

# Final instructions
echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Start the API server:"
echo "   ${BLUE}poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload${NC}"
echo ""
echo "2. Open your browser and visit:"
echo "   • API Documentation: ${BLUE}http://localhost:8000/docs${NC}"
echo "   • Alternative Docs: ${BLUE}http://localhost:8000/redoc${NC}"
echo ""
echo "3. Optional: Configure external API keys in .env for enhanced functionality:"
echo "   • OPENSTATES_API_KEY for legislative data"
echo "   • CONGRESS_GOV_API_KEY for federal bills"
echo "   • SMTP settings for email functionality"
echo ""
echo "📚 Documentation:"
echo "   • Development Guide: ${BLUE}DEVELOPMENT.md${NC}"
echo "   • Environment Reference: ${BLUE}ENV_REFERENCE.md${NC}"
echo "   • Full README: ${BLUE}README.md${NC}"
echo ""
echo "🐛 Troubleshooting:"
echo "   • Check database: ${BLUE}docker-compose logs db${NC}"
echo "   • Reset database: ${BLUE}docker-compose down -v && docker-compose up -d db${NC}"
echo "   • View API logs: Add ${BLUE}--log-level debug${NC} to uvicorn command"
echo ""
print_success "Happy coding! 🚀"
