# Test Environment Configuration
# This file is used for running tests with PostgreSQL

# Test Database Configuration
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_USER=modernaction_user
TEST_DB_PASSWORD=modernaction_password

# Use PostgreSQL for testing (same as production)
DATABASE_URL=postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction_test

# Security settings for testing
SECRET_KEY=test-secret-key-for-testing-only
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS settings for testing
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Environment
ENVIRONMENT=test
DEBUG=true
LOG_LEVEL=INFO

# Disable external services for testing
OPENSTATES_API_KEY=test-key
CONGRESS_GOV_API_KEY=test-key
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=<EMAIL>
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>
REDIS_URL=redis://localhost:6379
