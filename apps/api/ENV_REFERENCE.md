# Environment Variables Reference

Quick reference for configuring the ModernAction API environment variables.

## Quick Setup for Local Development

Create `apps/api/.env` with these minimal required settings:

```bash
# Required
DATABASE_URL=postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction
SECRET_KEY=your-secret-key-here-change-in-production

# Recommended for development
ENVIRONMENT=development
DEBUG=true
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

## Complete Variable Reference

### 🔴 Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://user:pass@localhost:5432/db` |
| `SECRET_KEY` | JWT signing secret (use secure random key) | `openssl rand -hex 32` |

### 🟡 Important Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `ENVIRONMENT` | Environment name | `development` | `development`, `staging`, `production` |
| `DEBUG` | Enable debug mode | `false` | `true`, `false` |
| `ALLOWED_ORIGINS` | CORS allowed origins | `http://localhost:3000` | `https://modernaction.org` |

### 🟢 Optional Variables

#### Authentication & Security
| Variable | Default | Description |
|----------|---------|-------------|
| `ALGORITHM` | `HS256` | JWT algorithm |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | `30` | JWT expiration time |
| `JWT_SECRET` | - | Legacy JWT secret |

#### API Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `API_V1_STR` | `/api/v1` | API version prefix |
| `PROJECT_NAME` | `ModernAction API` | API project name |
| `LOG_LEVEL` | `INFO` | Logging level |

#### External APIs
| Variable | Description | Required For |
|----------|-------------|--------------|
| `OPENSTATES_API_KEY` | OpenStates API key | State legislative data |
| `CONGRESS_GOV_API_KEY` | Congress.gov API key | Federal bill data |
| `GOOGLE_CIVIC_INFO_API_KEY` | Google Civic API key | Representative lookup |
| `HUGGING_FACE_API_KEY` | Hugging Face API key | AI/ML features |

#### Email Configuration (Choose One)

**SMTP Method:**
| Variable | Example | Description |
|----------|---------|-------------|
| `SMTP_HOST` | `smtp.gmail.com` | SMTP server host |
| `SMTP_PORT` | `587` | SMTP server port |
| `SMTP_USERNAME` | `<EMAIL>` | SMTP username |
| `SMTP_PASSWORD` | `your-app-password` | SMTP password |
| `FROM_EMAIL` | `<EMAIL>` | Default from email |
| `FROM_NAME` | `ModernAction` | Default from name |

**AWS SES Method:**
| Variable | Example | Description |
|----------|---------|-------------|
| `AWS_REGION` | `us-east-1` | AWS region |
| `AWS_ACCESS_KEY_ID` | `AKIA...` | AWS access key |
| `AWS_SECRET_ACCESS_KEY` | `secret...` | AWS secret key |
| `AWS_SES_FROM_EMAIL` | `<EMAIL>` | SES from email |
| `AWS_SES_FROM_NAME` | `ModernAction` | SES from name |

#### Background Services
| Variable | Default | Description |
|----------|---------|-------------|
| `REDIS_URL` | - | Redis connection string |

## Environment-Specific Examples

### Local Development
```bash
DATABASE_URL=postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction
SECRET_KEY=dev-secret-key-change-in-production
ENVIRONMENT=development
DEBUG=true
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
LOG_LEVEL=DEBUG
```

### Staging
```bash
DATABASE_URL=**************************************/modernaction
SECRET_KEY=secure-random-staging-key
ENVIRONMENT=staging
DEBUG=false
ALLOWED_ORIGINS=https://staging.modernaction.org
LOG_LEVEL=INFO
AWS_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>
```

### Production
```bash
DATABASE_URL=***********************************/modernaction
SECRET_KEY=very-secure-random-production-key
ENVIRONMENT=production
DEBUG=false
ALLOWED_ORIGINS=https://modernaction.org,https://www.modernaction.org
LOG_LEVEL=WARNING
AWS_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>
REDIS_URL=redis://prod-redis:6379/0
```

## Security Best Practices

### Secret Key Generation
```bash
# Generate a secure secret key
openssl rand -hex 32

# Or use Python
python -c "import secrets; print(secrets.token_hex(32))"
```

### Environment-Specific Security

#### Development
- ✅ Use simple secrets for convenience
- ✅ Enable DEBUG mode
- ✅ Use localhost origins
- ❌ Don't use production secrets

#### Production
- ✅ Use cryptographically secure random secrets
- ✅ Disable DEBUG mode
- ✅ Use HTTPS origins only
- ✅ Enable all security features
- ❌ Never commit secrets to version control

## Common Configurations

### Docker Development
```bash
# Use Docker PostgreSQL
DATABASE_URL=postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction

# Use Docker Redis
REDIS_URL=redis://localhost:6379/0
```

### External Database
```bash
# Use external PostgreSQL
DATABASE_URL=postgresql://username:<EMAIL>:5432/modernaction

# Use external Redis
REDIS_URL=redis://username:<EMAIL>:6379/0
```

### Email Testing
```bash
# Use Gmail SMTP for testing
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
```

## Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
docker-compose ps

# Test connection manually
psql postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction
```

### CORS Issues
```bash
# Add your frontend URL to ALLOWED_ORIGINS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://your-frontend.com
```

### Email Issues
```bash
# Test SMTP connection
telnet smtp.gmail.com 587

# Check AWS SES credentials
aws ses get-send-quota --region us-east-1
```

## Environment File Locations

- **Development**: `apps/api/.env`
- **Docker**: Mount as volume or use `--env-file`
- **Production**: Use secure secret management (AWS Secrets Manager, etc.)

## Validation

The API will validate required environment variables on startup and show helpful error messages if any are missing or invalid.

```bash
# Start the API to validate configuration
poetry run uvicorn app.main:app --reload
```

Look for startup messages indicating successful configuration loading.
