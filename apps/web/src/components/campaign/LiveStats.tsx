'use client';

// LiveStats component - displays real-time campaign statistics and progress
import React, { useEffect, useState } from 'react';
import { Campaign } from '../../types';

interface LiveStatsProps {
  campaign: Campaign;
  refreshInterval?: number; // in milliseconds
}

interface StatsData {
  totalActions: number;
  todayActions: number;
  participantsCount: number;
  completionRate: number;
  lastUpdated: string;
}

const LiveStats: React.FC<LiveStatsProps> = ({ 
  campaign, 
  refreshInterval = 30000 // 30 seconds default
}) => {
  const [stats, setStats] = useState<StatsData>({
    totalActions: campaign.actual_actions,
    todayActions: 0,
    participantsCount: 0,
    completionRate: campaign.completion_percentage,
    lastUpdated: new Date().toISOString()
  });
  const [isLoading, setIsLoading] = useState(false);

  // Simulate fetching live stats (replace with actual API call)
  const fetchLiveStats = async () => {
    setIsLoading(true);
    try {
      // TODO: Replace with actual API call to get live campaign stats
      // const response = await campaignApi.getCampaignStats(campaign.id);
      
      // Simulated data for now
      const simulatedStats: StatsData = {
        totalActions: campaign.actual_actions + Math.floor(Math.random() * 5),
        todayActions: Math.floor(Math.random() * 20),
        participantsCount: Math.floor(campaign.actual_actions * 0.7),
        completionRate: Math.min(
          ((campaign.actual_actions + Math.floor(Math.random() * 5)) / campaign.target_actions) * 100,
          100
        ),
        lastUpdated: new Date().toISOString()
      };
      
      setStats(simulatedStats);
    } catch (error) {
      console.error('Failed to fetch live stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Fetch initial stats
    fetchLiveStats();

    // Set up interval for live updates
    const interval = setInterval(fetchLiveStats, refreshInterval);

    // Cleanup interval on unmount
    return () => clearInterval(interval);
  }, [campaign.id, refreshInterval]);

  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  const formatLastUpdated = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return date.toLocaleDateString();
  };

  const getProgressColor = (percentage: number): string => {
    if (percentage >= 90) return 'bg-green-500';
    if (percentage >= 70) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Live Campaign Stats
        </h3>
        <div className="flex items-center text-xs text-gray-500">
          {isLoading && (
            <svg className="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          )}
          <span className="flex items-center">
            <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
            Updated {formatLastUpdated(stats.lastUpdated)}
          </span>
        </div>
      </div>

      {/* Main Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Campaign Progress</span>
          <span className="text-sm font-medium text-gray-900">
            {Math.round(stats.completionRate)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(stats.completionRate)}`}
            style={{ width: `${Math.min(stats.completionRate, 100)}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{formatNumber(stats.totalActions)} actions</span>
          <span>{formatNumber(campaign.target_actions)} target</span>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        {/* Total Actions */}
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {formatNumber(stats.totalActions)}
          </div>
          <div className="text-sm text-blue-800 font-medium">Total Actions</div>
          <div className="text-xs text-blue-600 mt-1">
            {stats.totalActions > campaign.actual_actions && (
              <span className="inline-flex items-center">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                +{stats.totalActions - campaign.actual_actions} recent
              </span>
            )}
          </div>
        </div>

        {/* Today's Actions */}
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {formatNumber(stats.todayActions)}
          </div>
          <div className="text-sm text-green-800 font-medium">Today</div>
          <div className="text-xs text-green-600 mt-1">
            Actions taken today
          </div>
        </div>

        {/* Participants */}
        <div className="text-center p-4 bg-purple-50 rounded-lg">
          <div className="text-2xl font-bold text-purple-600">
            {formatNumber(stats.participantsCount)}
          </div>
          <div className="text-sm text-purple-800 font-medium">Participants</div>
          <div className="text-xs text-purple-600 mt-1">
            People involved
          </div>
        </div>

        {/* Remaining Actions */}
        <div className="text-center p-4 bg-orange-50 rounded-lg">
          <div className="text-2xl font-bold text-orange-600">
            {formatNumber(Math.max(0, campaign.target_actions - stats.totalActions))}
          </div>
          <div className="text-sm text-orange-800 font-medium">Remaining</div>
          <div className="text-xs text-orange-600 mt-1">
            To reach goal
          </div>
        </div>
      </div>

      {/* Momentum Indicator */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Campaign Momentum</span>
          <div className="flex items-center">
            {stats.todayActions > 10 ? (
              <>
                <svg className="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium text-green-600">High</span>
              </>
            ) : stats.todayActions > 5 ? (
              <>
                <svg className="w-4 h-4 text-yellow-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium text-yellow-600">Moderate</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium text-red-600">Low</span>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveStats;
