import React, { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { CheckIcon } from '@heroicons/react/24/outline';
import { useOnboardingStore, ISSUE_CATEGORIES } from '../../stores/onboardingStore';

interface OnboardingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const OnboardingModal: React.FC<OnboardingModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { completeOnboarding } = useOnboardingStore();
  const [selectedIssues, setSelectedIssues] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleIssueToggle = (issue: string) => {
    setSelectedIssues(prev => 
      prev.includes(issue) 
        ? prev.filter(i => i !== issue)
        : [...prev, issue]
    );
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Complete onboarding with selected preferences
      completeOnboarding(selectedIssues);
      onClose();
    } catch (error) {
      console.error('Error saving onboarding preferences:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    // Complete onboarding with no preferences
    completeOnboarding([]);
    onClose();
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={() => {}}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title
                    as="h3"
                    className="text-xl font-semibold leading-6 text-gray-900"
                  >
                    Welcome to ModernAction!
                  </Dialog.Title>
                </div>

                {/* Welcome Message */}
                <div className="mb-6">
                  <p className="text-gray-700 mb-4">
                    Help us personalize your experience by selecting the issues you care about most. 
                    This will help us show you the most relevant campaigns and actions.
                  </p>
                  <p className="text-sm text-gray-500">
                    You can always change these preferences later in your settings.
                  </p>
                </div>

                {/* Issue Selection */}
                <div className="mb-8">
                  <h4 className="font-medium text-gray-900 mb-4">
                    What issues matter most to you? (Select all that apply)
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    {ISSUE_CATEGORIES.map((issue) => (
                      <button
                        key={issue}
                        type="button"
                        onClick={() => handleIssueToggle(issue)}
                        className={`
                          relative flex items-center justify-between p-3 rounded-lg border-2 transition-all duration-200
                          ${selectedIssues.includes(issue)
                            ? 'border-blue-500 bg-blue-50 text-blue-900'
                            : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                          }
                        `}
                        data-testid={`issue-${issue.toLowerCase().replace(/\s+/g, '-')}`}
                      >
                        <span className="font-medium text-sm">{issue}</span>
                        {selectedIssues.includes(issue) && (
                          <CheckIcon className="h-5 w-5 text-blue-600" aria-hidden="true" />
                        )}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Selection Summary */}
                {selectedIssues.length > 0 && (
                  <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>{selectedIssues.length}</strong> issue{selectedIssues.length !== 1 ? 's' : ''} selected
                    </p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={handleSkip}
                    className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    data-testid="onboarding-skip-button"
                  >
                    Skip for now
                  </button>
                  
                  <button
                    type="button"
                    onClick={handleSave}
                    disabled={isLoading}
                    className={`
                      px-6 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                      ${isLoading 
                        ? 'bg-gray-400 cursor-not-allowed' 
                        : 'bg-blue-600 hover:bg-blue-700'
                      }
                    `}
                    data-testid="onboarding-save-button"
                  >
                    {isLoading ? 'Saving...' : 'Get Started'}
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default OnboardingModal;
