import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import OnboardingModal from './OnboardingModal';
import { useOnboardingStore } from '../../stores/onboardingStore';

// Mock the onboarding store
jest.mock('../../stores/onboardingStore');

const mockCompleteOnboarding = jest.fn();
const mockUseOnboardingStore = useOnboardingStore as jest.MockedFunction<typeof useOnboardingStore>;

describe('OnboardingModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseOnboardingStore.mockReturnValue({
      hasCompletedOnboarding: false,
      issuePreferences: [],
      setHasCompletedOnboarding: jest.fn(),
      setIssuePreferences: jest.fn(),
      completeOnboarding: mockCompleteOnboarding,
      resetOnboarding: jest.fn(),
    });
  });

  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
  };

  it('renders the onboarding modal when open', () => {
    render(<OnboardingModal {...defaultProps} />);
    
    expect(screen.getByText('Welcome to ModernAction!')).toBeInTheDocument();
    expect(screen.getByText(/Help us personalize your experience/)).toBeInTheDocument();
    expect(screen.getByText('What issues matter most to you?')).toBeInTheDocument();
  });

  it('displays all issue categories as selectable options', () => {
    render(<OnboardingModal {...defaultProps} />);
    
    // Check that key issues are displayed
    expect(screen.getByTestId('issue-environment')).toBeInTheDocument();
    expect(screen.getByTestId('issue-healthcare')).toBeInTheDocument();
    expect(screen.getByTestId('issue-economic-justice')).toBeInTheDocument();
    expect(screen.getByTestId('issue-education')).toBeInTheDocument();
  });

  it('allows users to select and deselect issues', async () => {
    const user = userEvent.setup();
    render(<OnboardingModal {...defaultProps} />);
    
    const environmentButton = screen.getByTestId('issue-environment');
    const healthcareButton = screen.getByTestId('issue-healthcare');
    
    // Select environment issue
    await user.click(environmentButton);
    expect(environmentButton).toHaveClass('border-blue-500', 'bg-blue-50');
    
    // Select healthcare issue
    await user.click(healthcareButton);
    expect(healthcareButton).toHaveClass('border-blue-500', 'bg-blue-50');
    
    // Deselect environment issue
    await user.click(environmentButton);
    expect(environmentButton).not.toHaveClass('border-blue-500', 'bg-blue-50');
  });

  it('shows selection summary when issues are selected', async () => {
    const user = userEvent.setup();
    render(<OnboardingModal {...defaultProps} />);
    
    // Select two issues
    await user.click(screen.getByTestId('issue-environment'));
    await user.click(screen.getByTestId('issue-healthcare'));
    
    expect(screen.getByText('2 issues selected')).toBeInTheDocument();
  });

  it('calls completeOnboarding with selected preferences when Get Started is clicked', async () => {
    const user = userEvent.setup();
    const mockOnClose = jest.fn();
    render(<OnboardingModal {...defaultProps} onClose={mockOnClose} />);
    
    // Select some issues
    await user.click(screen.getByTestId('issue-environment'));
    await user.click(screen.getByTestId('issue-healthcare'));
    
    // Click Get Started
    await user.click(screen.getByTestId('onboarding-save-button'));
    
    await waitFor(() => {
      expect(mockCompleteOnboarding).toHaveBeenCalledWith(['Environment', 'Healthcare']);
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it('calls completeOnboarding with empty array when Skip is clicked', async () => {
    const user = userEvent.setup();
    const mockOnClose = jest.fn();
    render(<OnboardingModal {...defaultProps} onClose={mockOnClose} />);
    
    // Click Skip
    await user.click(screen.getByTestId('onboarding-skip-button'));
    
    expect(mockCompleteOnboarding).toHaveBeenCalledWith([]);
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('does not render when isOpen is false', () => {
    render(<OnboardingModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Welcome to ModernAction!')).not.toBeInTheDocument();
  });

  it('shows loading state when saving', async () => {
    const user = userEvent.setup();
    
    // Mock a delayed completion
    mockCompleteOnboarding.mockImplementation(() => {
      return new Promise(resolve => setTimeout(resolve, 100));
    });
    
    render(<OnboardingModal {...defaultProps} />);
    
    const saveButton = screen.getByTestId('onboarding-save-button');
    await user.click(saveButton);
    
    expect(screen.getByText('Saving...')).toBeInTheDocument();
    expect(saveButton).toBeDisabled();
  });
});
