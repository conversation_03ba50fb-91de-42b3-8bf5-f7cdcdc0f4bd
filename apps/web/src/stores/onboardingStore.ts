import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface OnboardingState {
  hasCompletedOnboarding: boolean;
  issuePreferences: string[];
  setHasCompletedOnboarding: (completed: boolean) => void;
  setIssuePreferences: (preferences: string[]) => void;
  completeOnboarding: (preferences: string[]) => void;
  resetOnboarding: () => void;
}

export const useOnboardingStore = create<OnboardingState>()(
  persist(
    (set) => ({
      hasCompletedOnboarding: false,
      issuePreferences: [],
      
      setHasCompletedOnboarding: (completed: boolean) =>
        set({ hasCompletedOnboarding: completed }),
      
      setIssuePreferences: (preferences: string[]) =>
        set({ issuePreferences: preferences }),
      
      completeOnboarding: (preferences: string[]) =>
        set({ 
          hasCompletedOnboarding: true, 
          issuePreferences: preferences 
        }),
      
      resetOnboarding: () =>
        set({ 
          hasCompletedOnboarding: false, 
          issuePreferences: [] 
        }),
    }),
    {
      name: 'modernaction-onboarding',
      // Only persist the state, not the functions
      partialize: (state) => ({
        hasCompletedOnboarding: state.hasCompletedOnboarding,
        issuePreferences: state.issuePreferences,
      }),
    }
  )
);

// Available issue categories for onboarding
export const ISSUE_CATEGORIES = [
  'Environment',
  'Healthcare',
  'Economic Justice',
  'Education',
  'Criminal Justice Reform',
  'Immigration',
  'Civil Rights',
  'Technology & Privacy',
  'Foreign Policy',
  'Infrastructure',
  'Housing',
  'Labor Rights',
] as const;

export type IssueCategory = typeof ISSUE_CATEGORIES[number];
