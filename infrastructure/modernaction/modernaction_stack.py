from aws_cdk import (
    Duration,
    Stack,
    aws_ec2 as ec2,
    aws_rds as rds,
    aws_secretsmanager as secretsmanager,
    aws_ecs as ecs,
    aws_ecs_patterns as ecs_patterns,
    aws_logs as logs,
    aws_iam as iam,
    aws_ses as ses,
    aws_certificatemanager as acm,
    aws_route53 as route53,
    aws_route53_targets as targets,
    aws_elasticloadbalancingv2 as elbv2,
    aws_lambda as _lambda,
    aws_events as events,
    aws_events_targets as targets_events,
    aws_sqs as sqs,
    RemovalPolicy,
    CfnOutput,
)
from constructs import Construct


class ModernActionStack(Stack):
    def __init__(
        self, 
        scope: Construct, 
        construct_id: str,
        environment: str,
        **kwargs
    ) -> None:
        super().__init__(scope, construct_id, **kwargs)
        
        self.environment = environment
        self.stack_name = f"modernaction-{environment}"
        
        # Create VPC with public and private subnets
        self.vpc = self._create_vpc()
        
        # Create RDS database
        self.database = self._create_database()
        
        # Create secrets for application configuration
        self.secrets = self._create_secrets()
        
        # Create ECS cluster and service
        self.ecs_cluster = self._create_ecs_cluster()
        
        # Create IAM roles
        self.iam_roles = self._create_iam_roles()

        # Create Lambda functions and background job infrastructure
        self.lambda_functions = self._create_lambda_functions()

        # Output important values
        self._create_outputs()

    def _create_vpc(self) -> ec2.Vpc:
        """Create VPC with public and private subnets"""
        return ec2.Vpc(
            self, 
            "ModernActionVPC",
            max_azs=2,
            nat_gateways=1,
            subnet_configuration=[
                ec2.SubnetConfiguration(
                    subnet_type=ec2.SubnetType.PUBLIC,
                    name="Public",
                    cidr_mask=24,
                ),
                ec2.SubnetConfiguration(
                    subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS,
                    name="Private",
                    cidr_mask=24,
                ),
            ],
            enable_dns_hostnames=True,
            enable_dns_support=True,
        )

    def _create_database(self) -> rds.DatabaseInstance:
        """Create RDS PostgreSQL database"""
        # Create security group for database
        db_security_group = ec2.SecurityGroup(
            self,
            "DatabaseSecurityGroup",
            vpc=self.vpc,
            description="Security group for ModernAction database",
            allow_all_outbound=False,
        )
        
        # Create database credentials in Secrets Manager
        db_credentials = rds.DatabaseSecret(
            self,
            "DatabaseCredentials",
            username="modernaction_admin",
            description="Database credentials for ModernAction",
        )
        
        # Create database subnet group
        db_subnet_group = rds.SubnetGroup(
            self,
            "DatabaseSubnetGroup",
            description="Subnet group for ModernAction database",
            vpc=self.vpc,
            vpc_subnets=ec2.SubnetSelection(
                subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
            ),
        )
        
        # Create database instance
        database = rds.DatabaseInstance(
            self,
            "ModernActionDatabase",
            engine=rds.DatabaseInstanceEngine.postgres(
                version=rds.PostgresEngineVersion.VER_15_4
            ),
            instance_type=ec2.InstanceType.of(
                ec2.InstanceClass.BURSTABLE3,
                ec2.InstanceSize.MICRO
            ),
            credentials=rds.Credentials.from_secret(db_credentials),
            database_name="modernaction",
            vpc=self.vpc,
            subnet_group=db_subnet_group,
            security_groups=[db_security_group],
            backup_retention=Duration.days(7),
            deletion_protection=self.environment == "prod",
            delete_automated_backups=self.environment != "prod",
            removal_policy=RemovalPolicy.DESTROY if self.environment != "prod" else RemovalPolicy.RETAIN,
            storage_encrypted=True,
            multi_az=self.environment == "prod",
            allocated_storage=20,
            max_allocated_storage=100,
            enable_performance_insights=True,
            performance_insight_retention=rds.PerformanceInsightRetention.DEFAULT,
            cloudwatch_logs_exports=["postgresql"],
            parameter_group=rds.ParameterGroup.from_parameter_group_name(
                self, "DatabaseParameterGroup", "default.postgres15"
            ),
        )
        
        return database

    def _create_secrets(self) -> dict:
        """Create secrets for application configuration"""
        secrets = {}
        
        # API Keys and external service credentials
        secrets["api_keys"] = secretsmanager.Secret(
            self,
            "ApiKeysSecret",
            description="API keys for external services (OpenStates, Google Civic Info, etc.)",
            secret_object_value={
                "OPENSTATES_API_KEY": secretsmanager.SecretValue.unsafe_plain_text(""),
                "GOOGLE_CIVIC_INFO_API_KEY": secretsmanager.SecretValue.unsafe_plain_text(""),
                "HUGGING_FACE_API_KEY": secretsmanager.SecretValue.unsafe_plain_text(""),
            },
        )
        
        # JWT and session secrets
        secrets["jwt_secret"] = secretsmanager.Secret(
            self,
            "JwtSecret",
            description="JWT signing secret for authentication",
            generate_secret_string=secretsmanager.SecretStringGenerator(
                secret_string_template='{"jwt_secret": ""}',
                generate_string_key="jwt_secret",
                exclude_characters=" %+~`#$&*()|[]{}:;<>?!'/\"\\",
                password_length=64,
            ),
        )
        
        # Application configuration
        secrets["app_config"] = secretsmanager.Secret(
            self,
            "AppConfigSecret",
            description="Application configuration secrets",
            secret_object_value={
                "SECRET_KEY": secretsmanager.SecretValue.unsafe_plain_text(""),
                "ENVIRONMENT": secretsmanager.SecretValue.unsafe_plain_text(self.environment),
                "CORS_ORIGINS": secretsmanager.SecretValue.unsafe_plain_text("http://localhost:3000"),
            },
        )
        
        return secrets

    def _create_ecs_cluster(self) -> ecs.Cluster:
        """Create ECS cluster for running the application"""
        cluster = ecs.Cluster(
            self,
            "ModernActionCluster",
            vpc=self.vpc,
            cluster_name=f"modernaction-{self.environment}",
            enable_logging=True,
            execute_command_configuration=ecs.ExecuteCommandConfiguration(
                logging=ecs.ExecuteCommandLogging.DEFAULT
            ),
        )
        
        return cluster

    def _create_iam_roles(self) -> dict:
        """Create IAM roles for different services"""
        roles = {}
        
        # Task execution role
        roles["task_execution_role"] = iam.Role(
            self,
            "TaskExecutionRole",
            assumed_by=iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AmazonECSTaskExecutionRolePolicy"
                )
            ],
        )
        
        # Task role for application
        roles["task_role"] = iam.Role(
            self,
            "TaskRole",
            assumed_by=iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
        )
        
        # Grant access to secrets
        for secret in self.secrets.values():
            secret.grant_read(roles["task_execution_role"])
            secret.grant_read(roles["task_role"])
        
        # Grant access to database secret
        self.database.secret.grant_read(roles["task_execution_role"])
        self.database.secret.grant_read(roles["task_role"])
        
        # Grant SES permissions
        roles["task_role"].add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "ses:SendEmail",
                    "ses:SendRawEmail",
                    "ses:GetSendQuota",
                    "ses:GetSendStatistics",
                ],
                resources=["*"],
            )
        )
        
        return roles

    def _create_outputs(self) -> None:
        """Create CloudFormation outputs"""
        # Lambda function outputs
        CfnOutput(
            self,
            "BillStatusUpdateLambdaArn",
            value=self.lambda_functions["bill_status_update"].function_arn,
            description="Bill Status Update Lambda Function ARN",
        )

        CfnOutput(
            self,
            "NotificationSenderLambdaArn",
            value=self.lambda_functions["notification_sender"].function_arn,
            description="Notification Sender Lambda Function ARN",
        )

        CfnOutput(
            self,
            "BillStatusQueueUrl",
            value=self.lambda_functions["bill_status_queue"].queue_url,
            description="Bill Status Notification Queue URL",
        )

        CfnOutput(
            self,
            "VpcId",
            value=self.vpc.vpc_id,
            description="VPC ID",
        )
        
        CfnOutput(
            self,
            "DatabaseEndpoint",
            value=self.database.instance_endpoint.hostname,
            description="Database endpoint",
        )
        
        CfnOutput(
            self,
            "DatabasePort",
            value=str(self.database.instance_endpoint.port),
            description="Database port",
        )
        
        CfnOutput(
            self,
            "DatabaseSecretArn",
            value=self.database.secret.secret_arn,
            description="Database secret ARN",
        )
        
        CfnOutput(
            self,
            "EcsClusterArn",
            value=self.ecs_cluster.cluster_arn,
            description="ECS Cluster ARN",
        )
        
        for name, secret in self.secrets.items():
            CfnOutput(
                self,
                f"{name.title()}SecretArn",
                value=secret.secret_arn,
                description=f"{name.title()} secret ARN",
            )

    def _create_lambda_functions(self) -> dict:
        """Create Lambda functions for background jobs"""
        lambda_functions = {}

        # Create SQS queue for bill status change notifications
        bill_status_queue = sqs.Queue(
            self,
            "BillStatusNotificationQueue",
            queue_name=f"modernaction-bill-status-notifications-{self.environment}",
            visibility_timeout=Duration.minutes(15),  # Match Lambda timeout
            retention_period=Duration.days(14),
            dead_letter_queue=sqs.DeadLetterQueue(
                max_receive_count=3,
                queue=sqs.Queue(
                    self,
                    "BillStatusNotificationDLQ",
                    queue_name=f"modernaction-bill-status-notifications-dlq-{self.environment}",
                    retention_period=Duration.days(14)
                )
            )
        )

        # Create Lambda execution role with necessary permissions
        lambda_execution_role = iam.Role(
            self,
            "BillStatusLambdaExecutionRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name("service-role/AWSLambdaBasicExecutionRole"),
                iam.ManagedPolicy.from_aws_managed_policy_name("service-role/AWSLambdaVPCAccessExecutionRole")
            ]
        )

        # Grant permissions to access secrets
        for secret in self.secrets.values():
            secret.grant_read(lambda_execution_role)

        # Grant permissions to access RDS
        self.database.grant_connect(lambda_execution_role)

        # Grant permissions to send messages to SQS
        bill_status_queue.grant_send_messages(lambda_execution_role)
        
        # Grant permissions to receive messages from SQS (for notification sender)
        bill_status_queue.grant_consume_messages(lambda_execution_role)
        
        # Grant permissions to send emails via SES
        lambda_execution_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "ses:SendEmail",
                    "ses:SendRawEmail",
                    "ses:GetSendQuota",
                    "ses:GetSendStatistics",
                ],
                resources=["*"],
            )
        )

        # Create security group for Lambda functions
        lambda_security_group = ec2.SecurityGroup(
            self,
            "LambdaSecurityGroup",
            vpc=self.vpc,
            description="Security group for Lambda functions",
            allow_all_outbound=True
        )

        # Allow Lambda to connect to RDS
        self.database.connections.allow_from(
            lambda_security_group,
            ec2.Port.tcp(5432),
            "Allow Lambda to connect to RDS"
        )

        # Bill Status Update Lambda Function
        bill_status_lambda = _lambda.Function(
            self,
            "BillStatusUpdateFunction",
            function_name=f"modernaction-bill-status-update-{self.environment}",
            runtime=_lambda.Runtime.PYTHON_3_11,
            handler="handler.lambda_handler",
            code=_lambda.Code.from_asset("apps/lambda/bill_status_update"),
            timeout=Duration.minutes(15),
            memory_size=512,
            role=lambda_execution_role,
            vpc=self.vpc,
            vpc_subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
            security_groups=[lambda_security_group],
            environment={
                "OPEN_STATES_API_KEY": self.secrets["api_keys"].secret_value_from_json("OPENSTATES_API_KEY").unsafe_unwrap(),
                "DB_HOST": self.database.instance_endpoint.hostname,
                "DB_PORT": str(self.database.instance_endpoint.port),
                "DB_NAME": "modernaction",
                "DB_USER": self.database.secret.secret_value_from_json("username").unsafe_unwrap(),
                "DB_PASSWORD": self.database.secret.secret_value_from_json("password").unsafe_unwrap(),
                "AWS_REGION": self.region,
                "SQS_QUEUE_URL": bill_status_queue.queue_url
            },
            log_retention=logs.RetentionDays.ONE_MONTH
        )

        # Schedule the bill status update function to run daily
        bill_status_schedule = events.Rule(
            self,
            "BillStatusUpdateSchedule",
            rule_name=f"modernaction-bill-status-update-schedule-{self.environment}",
            description="Daily trigger for bill status updates",
            schedule=events.Schedule.rate(Duration.days(1))
        )

        bill_status_schedule.add_target(
            targets_events.LambdaFunction(bill_status_lambda)
        )

        # Notification Sender Lambda Function
        notification_sender_lambda = _lambda.Function(
            self,
            "NotificationSenderFunction",
            function_name=f"modernaction-notification-sender-{self.environment}",
            runtime=_lambda.Runtime.PYTHON_3_11,
            handler="handler.lambda_handler",
            code=_lambda.Code.from_asset("apps/lambda/notification_sender"),
            timeout=Duration.minutes(15),
            memory_size=512,
            role=lambda_execution_role,
            vpc=self.vpc,
            vpc_subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
            security_groups=[lambda_security_group],
            environment={
                "DB_HOST": self.database.instance_endpoint.hostname,
                "DB_PORT": str(self.database.instance_endpoint.port),
                "DB_NAME": "modernaction",
                "DB_USER": self.database.secret.secret_value_from_json("username").unsafe_unwrap(),
                "DB_PASSWORD": self.database.secret.secret_value_from_json("password").unsafe_unwrap(),
                "AWS_REGION": self.region,
                "FROM_EMAIL": "<EMAIL>",
                "REPLY_TO_EMAIL": "<EMAIL>"
            },
            log_retention=logs.RetentionDays.ONE_MONTH
        )

        # Configure SQS to trigger the notification sender Lambda
        from aws_cdk import aws_lambda_event_sources as lambda_events
        
        notification_sender_lambda.add_event_source(
            lambda_events.SqsEventSource(
                queue=bill_status_queue,
                batch_size=10,  # Process up to 10 messages at once
                max_batching_window=Duration.seconds(30)  # Wait up to 30 seconds to batch messages
            )
        )

        lambda_functions["bill_status_update"] = bill_status_lambda
        lambda_functions["notification_sender"] = notification_sender_lambda
        lambda_functions["bill_status_queue"] = bill_status_queue

        return lambda_functions