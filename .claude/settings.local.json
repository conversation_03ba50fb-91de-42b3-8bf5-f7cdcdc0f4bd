{"permissions": {"allow": ["<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(poetry run alembic:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(poetry install:*)", "<PERSON><PERSON>(poetry lock:*)", "<PERSON><PERSON>(poetry run pytest:*)", "<PERSON><PERSON>(poetry show:*)", "<PERSON><PERSON>(poetry add:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(poetry run python:*)", "Bash(npm install:*)", "<PERSON><PERSON>(npx storybook:*)", "<PERSON><PERSON>(pkill:*)", "Bash(pip install:*)"], "deny": []}}